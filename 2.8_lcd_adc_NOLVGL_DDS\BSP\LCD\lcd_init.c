#include "lcd_init.h"
// #include "delay.h"

void LCD_GPIO_Init(void) {}

/******************************************************************************
      函数说明：LCD串行数据写入函数
      入口数据：dat  要写入的串行数据
      返回值：  无
******************************************************************************/
void LCD_Writ_Bus(u8 dat) {
  uint8_t recv_data = 0;

  LCD_CS_Clr();

  // 发送数据
  DL_SPI_transmitData8(TFTspi_INST, dat);
  // 等待SPI总线空闲
  while (DL_SPI_isBusy(TFTspi_INST))
    ;

  // 接收数据
  recv_data = DL_SPI_receiveData8(TFTspi_INST);
  // 等待SPI总线空闲
  while (DL_SPI_isBusy(TFTspi_INST))
    ;

  LCD_CS_Set();
}

/******************************************************************************
      函数说明：LCD写入数据
      入口数据：dat 写入的数据
      返回值：  无
******************************************************************************/
void LCD_WR_DATA8(u8 dat) { LCD_Writ_Bus(dat); }

/******************************************************************************
      函数说明：LCD写入数据
      入口数据：dat 写入的数据
      返回值：  无
******************************************************************************/
void LCD_WR_DATA(u16 dat) {
  LCD_Writ_Bus(dat >> 8);
  LCD_Writ_Bus(dat);
}

/******************************************************************************
      函数说明：LCD写入命令
      入口数据：dat 写入的命令
      返回值：  无
******************************************************************************/
void LCD_WR_REG(u8 dat) {
  LCD_DC_Clr(); // 写命令
  LCD_Writ_Bus(dat);
  LCD_DC_Set(); // 写数据
}

/******************************************************************************
      函数说明：设置起始和结束地址
      入口数据：x1,x2 设置列的起始和结束地址
                y1,y2 设置行的起始和结束地址
      返回值：  无
******************************************************************************/
void LCD_Address_Set(u16 x1, u16 y1, u16 x2, u16 y2) {
  if (USE_HORIZONTAL == 0) {
    LCD_WR_REG(0x2a); // 列地址设置
    LCD_WR_DATA(x1 + 1);
    LCD_WR_DATA(x2 + 1);
    LCD_WR_REG(0x2b); // 行地址设置
    LCD_WR_DATA(y1 + 1);
    LCD_WR_DATA(y2 + 1);
    LCD_WR_REG(0x2c); // 储存器写
  } else if (USE_HORIZONTAL == 1) {
    LCD_WR_REG(0x2a); // 列地址设置
    LCD_WR_DATA(x1 + 1);
    LCD_WR_DATA(x2 + 1);
    LCD_WR_REG(0x2b); // 行地址设置
    LCD_WR_DATA(y1 + 1);
    LCD_WR_DATA(y2 + 1);
    LCD_WR_REG(0x2c); // 储存器写
  } else if (USE_HORIZONTAL == 2) {
    LCD_WR_REG(0x2a); // 列地址设置
    LCD_WR_DATA(x1 + 1);
    LCD_WR_DATA(x2 + 1);
    LCD_WR_REG(0x2b); // 行地址设置
    // LCD_WR_DATA(y1+26);
    // LCD_WR_DATA(y2+26);
    LCD_WR_DATA(y1 + 1);
    LCD_WR_DATA(y2 + 1);
    LCD_WR_REG(0x2c); // 储存器写
  } else {
    LCD_WR_REG(0x2a); // 列地址设置
    LCD_WR_DATA(x1 + 1);
    LCD_WR_DATA(x2 + 1);
    LCD_WR_REG(0x2b); // 行地址设置
    LCD_WR_DATA(y1 + 1);
    LCD_WR_DATA(y2 + 1);
    LCD_WR_REG(0x2c); // 储存器写
  }
}



void LCD_Init(void) {
    
	delay_ms(200);
	LCD_RES_Clr(); // 复位
	delay_ms(200);
	LCD_RES_Set();
	delay_ms(200);
    
    LCD_WR_REG(0x11);  // Sleep out
    delay_ms(200);

	LCD_BLK_Set(); // 打开背光
	delay_ms(200);

    
    // Frame Rate Control (Normal Mode)
    LCD_WR_REG(0xB1);
    LCD_WR_DATA8(0x01);  // DIVA=01 (fosc/1)
    //LCD_WR_DATA8(0x2C);  // RTNA[7:0]=2Ch (44 frames)
    //LCD_WR_DATA8(0x2C);  // Front Porch=2Ch (44 lines)
	LCD_WR_DATA8(0x20);
	LCD_WR_DATA8(0x20);
    
    // Frame Rate Control (Idle Mode)
    LCD_WR_REG(0xB2);
    LCD_WR_DATA8(0x01);
    LCD_WR_DATA8(0x2C);
    LCD_WR_DATA8(0x2C);
    
    // Frame Rate Control (Partial Mode)
    LCD_WR_REG(0xB3);
    LCD_WR_DATA8(0x01);
    LCD_WR_DATA8(0x2C);
    LCD_WR_DATA8(0x2C);
    LCD_WR_DATA8(0x01);
    LCD_WR_DATA8(0x2C);
    LCD_WR_DATA8(0x2C);
    
    // Display Inversion Control
    LCD_WR_REG(0xB4);
    LCD_WR_DATA8(0x07);  // NLA=1, NLB=1, NLC=1 (Dot inversion)
    
    // Power Control 1
    LCD_WR_REG(0xC0);
    LCD_WR_DATA8(0xA2);  // AVDD=2xVCI, AVCL=-1xVCI, VDS=external
    LCD_WR_DATA8(0x02);  // VAP=1.425*VCI (GVDD=4.6V)
    
    // Power Control 2
    LCD_WR_REG(0xC1);
    LCD_WR_DATA8(0x02);  // SAP=0.8*VCI (DDVDH=4.6V)
    
    // Power Control 3
    LCD_WR_REG(0xC2);
    LCD_WR_DATA8(0x02);  // VRHP=0.8*VCI
    LCD_WR_DATA8(0x02);  // VRHN=-0.8*VCI
    
    // Power Control 4
    LCD_WR_REG(0xC3);
    LCD_WR_DATA8(0x22);  // VCM=1.5*VCI
    LCD_WR_DATA8(0x22);  // VCOMH=0.725*VCI
    
    // Power Control 5
    LCD_WR_REG(0xC4);
    LCD_WR_DATA8(0x22);  // VCOM amplitude=0.8*VCI
    LCD_WR_DATA8(0x22);  // VDV=0.8*VCI
    
    // VCOM Control 1
    LCD_WR_REG(0xC5);
    LCD_WR_DATA8(0x3C);  // VCOM=0.675*VCI
    
    // Memory Access Control
    //LCD_WR_REG(0x36);
    //LCD_WR_DATA8(0xC8);  // RGB, 竖屏，顶端开始
	LCD_WR_REG(0x36);
	if(USE_HORIZONTAL==0)LCD_WR_DATA8(0x08);
	else if(USE_HORIZONTAL==1)LCD_WR_DATA8(0xC8);
	else if(USE_HORIZONTAL==2)LCD_WR_DATA8(0x28);
	else LCD_WR_DATA8(0xA8);

    // Pixel Format
    LCD_WR_REG(0x3A);
    LCD_WR_DATA8(0x05);  // 16-bit/pixel (65K colors)
    
    // Positive Gamma Correction
    LCD_WR_REG(0xE0);
    LCD_WR_DATA8(0x02);
    LCD_WR_DATA8(0x1C);
    LCD_WR_DATA8(0x07);
    LCD_WR_DATA8(0x12);
    LCD_WR_DATA8(0x37);
    LCD_WR_DATA8(0x32);
    LCD_WR_DATA8(0x29);
    LCD_WR_DATA8(0x2D);
    LCD_WR_DATA8(0x29);
    LCD_WR_DATA8(0x25);
    LCD_WR_DATA8(0x2B);
    LCD_WR_DATA8(0x39);
    LCD_WR_DATA8(0x00);
    LCD_WR_DATA8(0x01);
    LCD_WR_DATA8(0x03);
    LCD_WR_DATA8(0x10);
    
    // Negative Gamma Correction
    LCD_WR_REG(0xE1);
    LCD_WR_DATA8(0x03);
    LCD_WR_DATA8(0x1D);
    LCD_WR_DATA8(0x07);
    LCD_WR_DATA8(0x06);
    LCD_WR_DATA8(0x2E);
    LCD_WR_DATA8(0x2C);
    LCD_WR_DATA8(0x29);
    LCD_WR_DATA8(0x2D);
    LCD_WR_DATA8(0x2E);
    LCD_WR_DATA8(0x2E);
    LCD_WR_DATA8(0x37);
    LCD_WR_DATA8(0x3F);
    LCD_WR_DATA8(0x00);
    LCD_WR_DATA8(0x00);
    LCD_WR_DATA8(0x02);
    LCD_WR_DATA8(0x10);
    
	// 对于240x320屏幕的横屏设置 (320x240)
	LCD_WR_REG(0x2A);   // 列地址设置
	LCD_WR_DATA8(0x00); // 起始列高字节
	LCD_WR_DATA8(0x00); // 起始列低字节
	LCD_WR_DATA8(0x01); // 结束列高字节 (320=0x0140)
	LCD_WR_DATA8(0x40); // 结束列低字节

	LCD_WR_REG(0x2B);   // 行地址设置
	LCD_WR_DATA8(0x00); // 起始行高字节
	LCD_WR_DATA8(0x00); // 起始行低字节
	LCD_WR_DATA8(0x00); // 结束行高字节 (240=0x00F0)
	LCD_WR_DATA8(0xF0); // 结束行低字节
    
    // Memory Write
    LCD_WR_REG(0x2C);  // 准备接收像素数据
    
    // Display On
    LCD_WR_REG(0x29);
}
