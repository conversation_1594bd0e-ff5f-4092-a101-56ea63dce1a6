******************************************************************************
            TI ARM Clang Linker PC v4.0.2                      
******************************************************************************
>> Linked Tue Jul 15 19:42:59 2025

OUTPUT FILE NAME:   <C_adc12_single_conversion.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000731


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000878  0001f788  R  X
  SRAM                  20200000   00008000  000012c4  00006d3c  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000878   00000878    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000750   00000750    r-x .text
  00000810    00000810    00000038   00000038    r-- .rodata
  00000848    00000848    00000030   00000030    r-- .cinit
20200000    20200000    000010c4   00000000    rw-
  20200000    20200000    000010c2   00000000    rw- .bss
  202010c2    202010c2    00000002   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000750     
                  000000c0    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000001a8    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000242    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000244    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000002d0    00000088     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00000358    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000003da    00000002     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000003dc    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000458    00000064     adc12_single_conversion.o (.text.ADC0_IRQHandler)
                  000004bc    00000062     libc.a : memset16.S.obj (.text:memset)
                  0000051e    00000002     --HOLE-- [fill = 0]
                  00000520    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  0000056c    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  000005b8    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000005f8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000638    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000674    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  000006ae    00000002     --HOLE-- [fill = 0]
                  000006b0    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000006e0    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000708    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00000730    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000758    00000024     adc12_single_conversion.o (.text.main)
                  0000077c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00000798    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  000007b0    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000007c2    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  000007d2    00000002     --HOLE-- [fill = 0]
                  000007d4    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000007e0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000007ea    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  000007f2    00000002     --HOLE-- [fill = 0]
                  000007f4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000007fc    00000006     libc.a : exit.c.obj (.text:abort)
                  00000802    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000806    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000080a    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000080e    00000002     --HOLE-- [fill = 0]

.cinit     0    00000848    00000030     
                  00000848    0000000c     (__TI_handler_table)
                  00000854    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0000085c    00000006     (.cinit..data.load) [load image, compression = lzss]
                  00000862    00000002     --HOLE-- [fill = 0]
                  00000864    00000010     (__TI_cinit_table)
                  00000874    00000004     --HOLE-- [fill = 0]

.rodata    0    00000810    00000038     
                  00000810    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00000828    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  0000083c    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00000844    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00000847    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000010c2     UNINITIALIZED
                  20200000    00001000     (.common:gAdcWaveform)
                  20201000    000000bc     (.common:gTIMER_0Backup)
                  202010bc    00000004     (.common:gAdcVoltage)
                  202010c0    00000002     (.common:gAdcResult)

.data      0    202010c2    00000002     UNINITIALIZED
                  202010c2    00000002     adc12_single_conversion.o (.data.gSampleIndex)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       adc12_single_conversion.o      136    0         4104   
       ti_msp_dl_config.o             398    55        188    
       startup_mspm0g350x_ticlang.o   6      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         540    247       4292   
                                                              
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     260    0         0      
       dl_dma.o                       76     0         0      
       dl_adc12.o                     64     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         410    0         0      
                                                              
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       memset16.S.obj                 98     0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_decompress_none.c.obj     18     0         0      
       copy_zero_init.c.obj           16     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         520    0         0      
                                                              
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       mulsf3.S.obj                   140    0         0      
       divsf3.S.obj                   130    0         0      
       muldsi3.S.obj                  58     0         0      
       floatunsisf.S.obj              40     0         0      
       aeabi_memset.S.obj             12     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         388    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      42        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   1862   289       4804   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00000864 records: 2, size/record: 8, table size: 16
	.bss: load addr=00000854, load size=00000008 bytes, run addr=20200000, run size=000010c2 bytes, compression=zero_init
	.data: load addr=0000085c, load size=00000006 bytes, run addr=202010c2, run size=00000002 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000848 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
00000459  ADC0_IRQHandler               
00000243  ADC1_IRQHandler               
00000243  AES_IRQHandler                
00000802  C$$EXIT                       
00000243  CANFD0_IRQHandler             
00000243  DAC0_IRQHandler               
000005b9  DL_ADC12_setClockConfig       
000007e1  DL_Common_delayCycles         
00000521  DL_DMA_initChannel            
000000c1  DL_Timer_initTimerMode        
0000077d  DL_Timer_setClockConfig       
00000243  DMA_IRQHandler                
00000243  Default_Handler               
00000243  GROUP0_IRQHandler             
00000243  GROUP1_IRQHandler             
00000803  HOSTexit                      
00000243  HardFault_Handler             
00000243  I2C0_IRQHandler               
00000243  I2C1_IRQHandler               
00000243  NMI_Handler                   
00000243  PendSV_Handler                
00000243  RTC_IRQHandler                
00000807  Reset_Handler                 
00000243  SPI0_IRQHandler               
00000243  SPI1_IRQHandler               
00000243  SVC_Handler                   
000002d1  SYSCFG_DL_ADC12_0_init        
00000799  SYSCFG_DL_DMA_CH0_init        
000007eb  SYSCFG_DL_DMA_init            
000003db  SYSCFG_DL_GPIO_init           
000006b1  SYSCFG_DL_SYSCTL_init         
0000056d  SYSCFG_DL_TIMER_0_init        
000006e1  SYSCFG_DL_init                
000005f9  SYSCFG_DL_initPower           
00000243  SysTick_Handler               
00000243  TIMA0_IRQHandler              
00000243  TIMA1_IRQHandler              
00000243  TIMG0_IRQHandler              
00000243  TIMG12_IRQHandler             
00000243  TIMG6_IRQHandler              
00000243  TIMG7_IRQHandler              
00000243  TIMG8_IRQHandler              
00000243  UART0_IRQHandler              
00000243  UART1_IRQHandler              
00000243  UART2_IRQHandler              
00000243  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000864  __TI_CINIT_Base               
00000874  __TI_CINIT_Limit              
00000874  __TI_CINIT_Warm               
00000848  __TI_Handler_Table_Base       
00000854  __TI_Handler_Table_Limit      
00000639  __TI_auto_init_nobinit_nopinit
000003dd  __TI_decompress_lzss          
000007b1  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
000007c3  __TI_zero_init                
00000359  __aeabi_fdiv                  
00000245  __aeabi_fmul                  
000007d5  __aeabi_memclr                
000007d5  __aeabi_memclr4               
000007d5  __aeabi_memclr8               
000007f5  __aeabi_memcpy                
000007f5  __aeabi_memcpy4               
000007f5  __aeabi_memcpy8               
00000709  __aeabi_ui2f                  
ffffffff  __binit__                     
00000359  __divsf3                      
00000709  __floatunsisf                 
UNDEFED   __mpu_init                    
00000675  __muldsi3                     
00000245  __mulsf3                      
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000731  _c_int00_noargs               
UNDEFED   _system_post_cinit            
0000080b  _system_pre_init              
000007fd  abort                         
ffffffff  binit                         
202010c0  gAdcResult                    
202010bc  gAdcVoltage                   
20200000  gAdcWaveform                  
202010c2  gSampleIndex                  
20201000  gTIMER_0Backup                
00000000  interruptVectors              
00000759  main                          
000001a9  memcpy                        
000004bd  memset                        


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  DL_Timer_initTimerMode        
000001a9  memcpy                        
00000200  __STACK_SIZE                  
00000243  ADC1_IRQHandler               
00000243  AES_IRQHandler                
00000243  CANFD0_IRQHandler             
00000243  DAC0_IRQHandler               
00000243  DMA_IRQHandler                
00000243  Default_Handler               
00000243  GROUP0_IRQHandler             
00000243  GROUP1_IRQHandler             
00000243  HardFault_Handler             
00000243  I2C0_IRQHandler               
00000243  I2C1_IRQHandler               
00000243  NMI_Handler                   
00000243  PendSV_Handler                
00000243  RTC_IRQHandler                
00000243  SPI0_IRQHandler               
00000243  SPI1_IRQHandler               
00000243  SVC_Handler                   
00000243  SysTick_Handler               
00000243  TIMA0_IRQHandler              
00000243  TIMA1_IRQHandler              
00000243  TIMG0_IRQHandler              
00000243  TIMG12_IRQHandler             
00000243  TIMG6_IRQHandler              
00000243  TIMG7_IRQHandler              
00000243  TIMG8_IRQHandler              
00000243  UART0_IRQHandler              
00000243  UART1_IRQHandler              
00000243  UART2_IRQHandler              
00000243  UART3_IRQHandler              
00000245  __aeabi_fmul                  
00000245  __mulsf3                      
000002d1  SYSCFG_DL_ADC12_0_init        
00000359  __aeabi_fdiv                  
00000359  __divsf3                      
000003db  SYSCFG_DL_GPIO_init           
000003dd  __TI_decompress_lzss          
00000459  ADC0_IRQHandler               
000004bd  memset                        
00000521  DL_DMA_initChannel            
0000056d  SYSCFG_DL_TIMER_0_init        
000005b9  DL_ADC12_setClockConfig       
000005f9  SYSCFG_DL_initPower           
00000639  __TI_auto_init_nobinit_nopinit
00000675  __muldsi3                     
000006b1  SYSCFG_DL_SYSCTL_init         
000006e1  SYSCFG_DL_init                
00000709  __aeabi_ui2f                  
00000709  __floatunsisf                 
00000731  _c_int00_noargs               
00000759  main                          
0000077d  DL_Timer_setClockConfig       
00000799  SYSCFG_DL_DMA_CH0_init        
000007b1  __TI_decompress_none          
000007c3  __TI_zero_init                
000007d5  __aeabi_memclr                
000007d5  __aeabi_memclr4               
000007d5  __aeabi_memclr8               
000007e1  DL_Common_delayCycles         
000007eb  SYSCFG_DL_DMA_init            
000007f5  __aeabi_memcpy                
000007f5  __aeabi_memcpy4               
000007f5  __aeabi_memcpy8               
000007fd  abort                         
00000802  C$$EXIT                       
00000803  HOSTexit                      
00000807  Reset_Handler                 
0000080b  _system_pre_init              
00000848  __TI_Handler_Table_Base       
00000854  __TI_Handler_Table_Limit      
00000864  __TI_CINIT_Base               
00000874  __TI_CINIT_Limit              
00000874  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  gAdcWaveform                  
20201000  gTIMER_0Backup                
202010bc  gAdcVoltage                   
202010c0  gAdcResult                    
202010c2  gSampleIndex                  
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[104 symbols]
