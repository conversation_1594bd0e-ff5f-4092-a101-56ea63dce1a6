<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\bin\tiarmlnk -IG:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib -o double _adc_dma.out -mdouble _adc_dma.map -iC:/TI/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/double _adc_dma -iC:/Users/<USER>/workspace_ccstheia/double _adc_dma/Debug/syscfg -iG:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=double _adc_dma_linkInfo.xml --rom_model ./adc_to_uart.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x6876602b</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\double _adc_dma\Debug\double _adc_dma.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x931</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\double _adc_dma\Debug\.\</path>
         <kind>object</kind>
         <file>adc_to_uart.o</file>
         <name>adc_to_uart.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\double _adc_dma\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\double _adc_dma\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>C:\Users\<USER>\workspace_ccstheia\double _adc_dma\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_adc12.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_dma.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-2b">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d5">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.text.main</name>
         <load_address>0x1a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a8</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.text.reconfigure_and_restart_dma</name>
         <load_address>0x268</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x268</run_address>
         <size>0xa4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.text.SYSCFG_DL_ADC12_1_init</name>
         <load_address>0x30c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x30c</run_address>
         <size>0x94</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.text.SYSCFG_DL_ADC12_0_init</name>
         <load_address>0x3a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3a0</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x430</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x430</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x4ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4ac</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.text.SYSCFG_DL_TIMER_0_init</name>
         <load_address>0x514</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x514</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.text.DL_ADC12_initSingleSample</name>
         <load_address>0x56c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x56c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-115">
         <name>.text.DL_DMA_initChannel</name>
         <load_address>0x5b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5b8</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.text.DL_ADC12_configConversionMem</name>
         <load_address>0x604</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x604</run_address>
         <size>0x4a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.text.DL_ADC12_setClockConfig</name>
         <load_address>0x650</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x650</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-99">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x690</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x690</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text.DL_Timer_setPublisherChanID</name>
         <load_address>0x6cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6cc</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text.DL_DMA_setTransferSize</name>
         <load_address>0x704</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x704</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-114">
         <name>.text.DL_DMA_setTransferSize</name>
         <load_address>0x734</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x734</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.text.DL_ADC12_setDMASamplesCnt</name>
         <load_address>0x764</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x764</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-100">
         <name>.text.SYSCFG_DL_DMA_CH1_init</name>
         <load_address>0x790</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x790</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-83">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x7bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7bc</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-88">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x7e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7e8</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x814</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x814</run_address>
         <size>0x2a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x83e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x83e</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.text.DL_DMA_setDestAddr</name>
         <load_address>0x868</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x868</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-89">
         <name>.text.DL_DMA_setSrcAddr</name>
         <load_address>0x890</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x890</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.DL_Timer_enableEvent</name>
         <load_address>0x8b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8b8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text.SYSCFG_DL_DMA_CH0_init</name>
         <load_address>0x8e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8e0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x908</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x908</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x930</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x930</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.text.DL_DMA_enableChannel</name>
         <load_address>0x958</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x958</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.ADC0_IRQHandler</name>
         <load_address>0x980</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x980</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.ADC1_IRQHandler</name>
         <load_address>0x9a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9a0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.text.DL_ADC12_setPowerDownMode</name>
         <load_address>0x9c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9c0</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.text.DL_ADC12_clearInterruptStatus</name>
         <load_address>0x9e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9e0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.text.DL_ADC12_enableDMA</name>
         <load_address>0x9fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9fc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text.DL_ADC12_enableDMATrigger</name>
         <load_address>0xa18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa18</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.DL_ADC12_enableInterrupt</name>
         <load_address>0xa34</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa34</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-df">
         <name>.text.DL_SYSCTL_setMCLKDivider</name>
         <load_address>0xa50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa50</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0xa6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa6c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-de">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0xa88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa88</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0xaa4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaa4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.text.DL_ADC12_setSubscriberChanID</name>
         <load_address>0xac0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xac0</run_address>
         <size>0x1a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.text.DL_ADC12_enablePower</name>
         <load_address>0xadc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xadc</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.text.DL_ADC12_reset</name>
         <load_address>0xaf4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaf4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.text.DL_ADC12_setSampleTime0</name>
         <load_address>0xb0c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb0c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0xb24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb24</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0xb3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb3c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0xb54</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb54</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0xb6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb6c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0xb84</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb84</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0xb9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb9c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text.DL_Timer_reset</name>
         <load_address>0xbb4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbb4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.text.DL_Timer_startCounter</name>
         <load_address>0xbcc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbcc</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.text.DL_Timer_stopCounter</name>
         <load_address>0xbe4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbe4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text.DL_ADC12_enableConversions</name>
         <load_address>0xbfc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbfc</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0xc12</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc12</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0xc28</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc28</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0xc3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc3c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.text.DL_ADC12_getPendingInterrupt</name>
         <load_address>0xc50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc50</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-79">
         <name>.text:TI_memcpy_small</name>
         <load_address>0xc62</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc62</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0xc74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc74</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0xc88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc88</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-52">
         <name>.text:decompress:ZI</name>
         <load_address>0xc98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc98</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.text:TI_memset_small</name>
         <load_address>0xca8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xca8</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.text.SYSCFG_DL_DMA_init</name>
         <load_address>0xcb6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcb6</run_address>
         <size>0xc</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-65">
         <name>.text.__aeabi_memclr</name>
         <load_address>0xcc4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcc4</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0xcd0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcd0</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0xcdc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcdc</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-92">
         <name>.text:abort</name>
         <load_address>0xce4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xce4</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0xcea</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcea</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.text.HOSTexit</name>
         <load_address>0xcee</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcee</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0xcf2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcf2</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.text._system_pre_init</name>
         <load_address>0xcf6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcf6</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-15d">
         <name>__TI_handler_table</name>
         <load_address>0xd58</load_address>
         <readonly>true</readonly>
         <run_address>0xd58</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-160">
         <name>.cinit..bss.load</name>
         <load_address>0xd64</load_address>
         <readonly>true</readonly>
         <run_address>0xd64</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-15f">
         <name>.cinit..data.load</name>
         <load_address>0xd6c</load_address>
         <readonly>true</readonly>
         <run_address>0xd6c</run_address>
         <size>0x6</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-15e">
         <name>__TI_cinit_table</name>
         <load_address>0xd74</load_address>
         <readonly>true</readonly>
         <run_address>0xd74</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-119">
         <name>.rodata.gDMA_CH0Config</name>
         <load_address>0xd00</load_address>
         <readonly>true</readonly>
         <run_address>0xd00</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.rodata.gDMA_CH1Config</name>
         <load_address>0xd18</load_address>
         <readonly>true</readonly>
         <run_address>0xd18</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.rodata.gTIMER_0TimerConfig</name>
         <load_address>0xd30</load_address>
         <readonly>true</readonly>
         <run_address>0xd30</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.rodata.gADC12_0ClockConfig</name>
         <load_address>0xd44</load_address>
         <readonly>true</readonly>
         <run_address>0xd44</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.rodata.gADC12_1ClockConfig</name>
         <load_address>0xd4c</load_address>
         <readonly>true</readonly>
         <run_address>0xd4c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.rodata.gTIMER_0ClockConfig</name>
         <load_address>0xd54</load_address>
         <readonly>true</readonly>
         <run_address>0xd54</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-127">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-5d">
         <name>.data.gADC0_DMADone</name>
         <load_address>0x202010bc</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202010bc</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-60">
         <name>.data.gADC1_DMADone</name>
         <load_address>0x202010bd</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202010bd</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-90">
         <name>.common:gADC0_Data</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x800</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-91">
         <name>.common:gADC1_Data</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200800</run_address>
         <size>0x800</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-b9">
         <name>.common:gTIMER_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20201000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-162">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_abbrev</name>
         <load_address>0x18c</load_address>
         <run_address>0x18c</run_address>
         <size>0x1d9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0x365</load_address>
         <run_address>0x365</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_abbrev</name>
         <load_address>0x3d2</load_address>
         <run_address>0x3d2</run_address>
         <size>0x171</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_abbrev</name>
         <load_address>0x543</load_address>
         <run_address>0x543</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.debug_abbrev</name>
         <load_address>0x5a5</load_address>
         <run_address>0x5a5</run_address>
         <size>0x180</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_abbrev</name>
         <load_address>0x725</load_address>
         <run_address>0x725</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0x9ab</load_address>
         <run_address>0x9ab</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_abbrev</name>
         <load_address>0xa5a</load_address>
         <run_address>0xa5a</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_abbrev</name>
         <load_address>0xbca</load_address>
         <run_address>0xbca</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_abbrev</name>
         <load_address>0xc03</load_address>
         <run_address>0xc03</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0xcc5</load_address>
         <run_address>0xcc5</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0xd35</load_address>
         <run_address>0xd35</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_abbrev</name>
         <load_address>0xdc2</load_address>
         <run_address>0xdc2</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_abbrev</name>
         <load_address>0xe5a</load_address>
         <run_address>0xe5a</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_abbrev</name>
         <load_address>0xe86</load_address>
         <run_address>0xe86</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_abbrev</name>
         <load_address>0xead</load_address>
         <run_address>0xead</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_abbrev</name>
         <load_address>0xed2</load_address>
         <run_address>0xed2</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_abbrev</name>
         <load_address>0xef7</load_address>
         <run_address>0xef7</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-164">
         <name>.debug_abbrev</name>
         <load_address>0xf1c</load_address>
         <run_address>0xf1c</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13de</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_info</name>
         <load_address>0x13de</load_address>
         <run_address>0x13de</run_address>
         <size>0x2d82</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x4160</load_address>
         <run_address>0x4160</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_info</name>
         <load_address>0x41e0</load_address>
         <run_address>0x41e0</run_address>
         <size>0x745</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_info</name>
         <load_address>0x4925</load_address>
         <run_address>0x4925</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_info</name>
         <load_address>0x499a</load_address>
         <run_address>0x499a</run_address>
         <size>0x6ea</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_info</name>
         <load_address>0x5084</load_address>
         <run_address>0x5084</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x81f6</load_address>
         <run_address>0x81f6</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_info</name>
         <load_address>0x8619</load_address>
         <run_address>0x8619</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_info</name>
         <load_address>0x8d5d</load_address>
         <run_address>0x8d5d</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_info</name>
         <load_address>0x8da3</load_address>
         <run_address>0x8da3</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0x8f35</load_address>
         <run_address>0x8f35</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_info</name>
         <load_address>0x8ffb</load_address>
         <run_address>0x8ffb</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_info</name>
         <load_address>0x9177</load_address>
         <run_address>0x9177</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_info</name>
         <load_address>0x926f</load_address>
         <run_address>0x926f</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_info</name>
         <load_address>0x92aa</load_address>
         <run_address>0x92aa</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_info</name>
         <load_address>0x9443</load_address>
         <run_address>0x9443</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_info</name>
         <load_address>0x95f8</load_address>
         <run_address>0x95f8</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_info</name>
         <load_address>0x98f2</load_address>
         <run_address>0x98f2</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-163">
         <name>.debug_info</name>
         <load_address>0x9b36</load_address>
         <run_address>0x9b36</run_address>
         <size>0xaf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x68</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_ranges</name>
         <load_address>0x68</load_address>
         <run_address>0x68</run_address>
         <size>0x160</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x1c8</load_address>
         <run_address>0x1c8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_ranges</name>
         <load_address>0x1e0</load_address>
         <run_address>0x1e0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_ranges</name>
         <load_address>0x1f8</load_address>
         <run_address>0x1f8</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x3d0</load_address>
         <run_address>0x3d0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_ranges</name>
         <load_address>0x418</load_address>
         <run_address>0x418</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_ranges</name>
         <load_address>0x460</load_address>
         <run_address>0x460</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x478</load_address>
         <run_address>0x478</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_ranges</name>
         <load_address>0x4c8</load_address>
         <run_address>0x4c8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_ranges</name>
         <load_address>0x4e0</load_address>
         <run_address>0x4e0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_ranges</name>
         <load_address>0x508</load_address>
         <run_address>0x508</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_ranges</name>
         <load_address>0x530</load_address>
         <run_address>0x530</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb95</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_str</name>
         <load_address>0xb95</load_address>
         <run_address>0xb95</run_address>
         <size>0x23cf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x2f64</load_address>
         <run_address>0x2f64</run_address>
         <size>0x166</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_str</name>
         <load_address>0x30ca</load_address>
         <run_address>0x30ca</run_address>
         <size>0x631</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.debug_str</name>
         <load_address>0x36fb</load_address>
         <run_address>0x36fb</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.debug_str</name>
         <load_address>0x3868</load_address>
         <run_address>0x3868</run_address>
         <size>0x64a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_str</name>
         <load_address>0x3eb2</load_address>
         <run_address>0x3eb2</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_str</name>
         <load_address>0x5c7e</load_address>
         <run_address>0x5c7e</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_str</name>
         <load_address>0x5ea3</load_address>
         <run_address>0x5ea3</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_str</name>
         <load_address>0x61d2</load_address>
         <run_address>0x61d2</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_str</name>
         <load_address>0x62c7</load_address>
         <run_address>0x62c7</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x6462</load_address>
         <run_address>0x6462</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0x65ca</load_address>
         <run_address>0x65ca</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_str</name>
         <load_address>0x679f</load_address>
         <run_address>0x679f</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-102">
         <name>.debug_str</name>
         <load_address>0x68e7</load_address>
         <run_address>0x68e7</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x118</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_frame</name>
         <load_address>0x118</load_address>
         <run_address>0x118</run_address>
         <size>0x3f4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0x50c</load_address>
         <run_address>0x50c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_frame</name>
         <load_address>0x53c</load_address>
         <run_address>0x53c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_frame</name>
         <load_address>0x588</load_address>
         <run_address>0x588</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_frame</name>
         <load_address>0x5a8</load_address>
         <run_address>0x5a8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_frame</name>
         <load_address>0x5d8</load_address>
         <run_address>0x5d8</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x9e0</load_address>
         <run_address>0x9e0</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_frame</name>
         <load_address>0xa70</load_address>
         <run_address>0xa70</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_frame</name>
         <load_address>0xb70</load_address>
         <run_address>0xb70</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_frame</name>
         <load_address>0xb90</load_address>
         <run_address>0xb90</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0xbc8</load_address>
         <run_address>0xbc8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0xbf0</load_address>
         <run_address>0xbf0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_frame</name>
         <load_address>0xc20</load_address>
         <run_address>0xc20</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_frame</name>
         <load_address>0xc50</load_address>
         <run_address>0xc50</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x47a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_line</name>
         <load_address>0x47a</load_address>
         <run_address>0x47a</run_address>
         <size>0x9f1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_line</name>
         <load_address>0xe6b</load_address>
         <run_address>0xe6b</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_line</name>
         <load_address>0xf23</load_address>
         <run_address>0xf23</run_address>
         <size>0x27f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_line</name>
         <load_address>0x11a2</load_address>
         <run_address>0x11a2</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-116">
         <name>.debug_line</name>
         <load_address>0x131a</load_address>
         <run_address>0x131a</run_address>
         <size>0x248</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_line</name>
         <load_address>0x1562</load_address>
         <run_address>0x1562</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x2cd0</load_address>
         <run_address>0x2cd0</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_line</name>
         <load_address>0x2eac</load_address>
         <run_address>0x2eac</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_line</name>
         <load_address>0x33c6</load_address>
         <run_address>0x33c6</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_line</name>
         <load_address>0x3404</load_address>
         <run_address>0x3404</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x3502</load_address>
         <run_address>0x3502</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x35c2</load_address>
         <run_address>0x35c2</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_line</name>
         <load_address>0x378a</load_address>
         <run_address>0x378a</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_line</name>
         <load_address>0x37f1</load_address>
         <run_address>0x37f1</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_line</name>
         <load_address>0x3832</load_address>
         <run_address>0x3832</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_line</name>
         <load_address>0x38d6</load_address>
         <run_address>0x38d6</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_line</name>
         <load_address>0x3990</load_address>
         <run_address>0x3990</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.debug_line</name>
         <load_address>0x3a30</load_address>
         <run_address>0x3a30</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.debug_loc</name>
         <load_address>0xc7</load_address>
         <run_address>0xc7</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_loc</name>
         <load_address>0xda</load_address>
         <run_address>0xda</run_address>
         <size>0xd0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_loc</name>
         <load_address>0x1aa</load_address>
         <run_address>0x1aa</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_loc</name>
         <load_address>0x1bd1</load_address>
         <run_address>0x1bd1</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_loc</name>
         <load_address>0x1ca9</load_address>
         <run_address>0x1ca9</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_loc</name>
         <load_address>0x20cd</load_address>
         <run_address>0x20cd</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x2239</load_address>
         <run_address>0x2239</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_loc</name>
         <load_address>0x22a8</load_address>
         <run_address>0x22a8</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_loc</name>
         <load_address>0x240f</load_address>
         <run_address>0x240f</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_aranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_aranges</name>
         <load_address>0x70</load_address>
         <run_address>0x70</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0xc40</size>
         <contents>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0xd58</load_address>
         <run_address>0xd58</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-15e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0xd00</load_address>
         <run_address>0xd00</run_address>
         <size>0x58</size>
         <contents>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-ea"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-127"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202010bc</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-60"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x10bc</size>
         <contents>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-b9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-162"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11e" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11f" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-120" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-121" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-122" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-123" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-125" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-141" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf2b</size>
         <contents>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-164"/>
         </contents>
      </logical_group>
      <logical_group id="lg-143" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x9be5</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-163"/>
         </contents>
      </logical_group>
      <logical_group id="lg-145" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x558</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-ad"/>
         </contents>
      </logical_group>
      <logical_group id="lg-147" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x69d0</size>
         <contents>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-102"/>
         </contents>
      </logical_group>
      <logical_group id="lg-149" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc70</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-bd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-14b" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3ab0</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-b0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-14d" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2435</size>
         <contents>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-98"/>
         </contents>
      </logical_group>
      <logical_group id="lg-157" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x98</size>
         <contents>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-ae"/>
         </contents>
      </logical_group>
      <logical_group id="lg-161" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-172" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xd88</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-173" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x10be</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-174" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0xd88</used_space>
         <unused_space>0x1f278</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0xc40</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xd00</start_address>
               <size>0x58</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xd58</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0xd88</start_address>
               <size>0x1f278</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x12be</used_space>
         <unused_space>0x6d42</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-123"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-125"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x10bc</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x202010bc</start_address>
               <size>0x2</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202010be</start_address>
               <size>0x6d42</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0xd64</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x10bc</run_size>
            <compression>zero_init</compression>
         </cprec>
         <cprec>
            <name>.data</name>
            <load_address>0xd6c</load_address>
            <load_size>0x6</load_size>
            <run_address>0x202010bc</run_address>
            <run_size>0x2</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0xd74</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0xd84</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0xd84</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0xd58</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0xd64</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-67">
         <name>main</name>
         <value>0x1a9</value>
         <object_component_ref idref="oc-6e"/>
      </symbol>
      <symbol id="sm-68">
         <name>reconfigure_and_restart_dma</name>
         <value>0x269</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-69">
         <name>gADC0_Data</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-6a">
         <name>gADC1_Data</name>
         <value>0x20200800</value>
      </symbol>
      <symbol id="sm-6b">
         <name>ADC0_IRQHandler</name>
         <value>0x981</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-6c">
         <name>ADC1_IRQHandler</name>
         <value>0x9a1</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-111">
         <name>SYSCFG_DL_init</name>
         <value>0x7bd</value>
         <object_component_ref idref="oc-83"/>
      </symbol>
      <symbol id="sm-112">
         <name>SYSCFG_DL_initPower</name>
         <value>0x4ad</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-113">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x909</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-114">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x815</value>
         <object_component_ref idref="oc-b4"/>
      </symbol>
      <symbol id="sm-115">
         <name>SYSCFG_DL_TIMER_0_init</name>
         <value>0x515</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-116">
         <name>SYSCFG_DL_ADC12_0_init</name>
         <value>0x3a1</value>
         <object_component_ref idref="oc-b6"/>
      </symbol>
      <symbol id="sm-117">
         <name>SYSCFG_DL_ADC12_1_init</name>
         <value>0x30d</value>
         <object_component_ref idref="oc-b7"/>
      </symbol>
      <symbol id="sm-118">
         <name>SYSCFG_DL_DMA_init</name>
         <value>0xcb7</value>
         <object_component_ref idref="oc-b8"/>
      </symbol>
      <symbol id="sm-119">
         <name>gTIMER_0Backup</name>
         <value>0x20201000</value>
      </symbol>
      <symbol id="sm-11a">
         <name>SYSCFG_DL_DMA_CH0_init</name>
         <value>0x8e1</value>
         <object_component_ref idref="oc-ff"/>
      </symbol>
      <symbol id="sm-11b">
         <name>SYSCFG_DL_DMA_CH1_init</name>
         <value>0x791</value>
         <object_component_ref idref="oc-100"/>
      </symbol>
      <symbol id="sm-126">
         <name>Default_Handler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-127">
         <name>Reset_Handler</name>
         <value>0xcf3</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-128">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-129">
         <name>NMI_Handler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12a">
         <name>HardFault_Handler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12b">
         <name>SVC_Handler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12c">
         <name>PendSV_Handler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12d">
         <name>SysTick_Handler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12e">
         <name>GROUP0_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-12f">
         <name>GROUP1_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-130">
         <name>TIMG8_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-131">
         <name>UART3_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-132">
         <name>CANFD0_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-133">
         <name>DAC0_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-134">
         <name>SPI0_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-135">
         <name>SPI1_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-136">
         <name>UART1_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-137">
         <name>UART2_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-138">
         <name>UART0_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-139">
         <name>TIMG0_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13a">
         <name>TIMG6_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13b">
         <name>TIMA0_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13c">
         <name>TIMA1_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13d">
         <name>TIMG7_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13e">
         <name>TIMG12_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-13f">
         <name>I2C0_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-140">
         <name>I2C1_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-141">
         <name>AES_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-142">
         <name>RTC_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-143">
         <name>DMA_IRQHandler</name>
         <value>0xceb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-144">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-145">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-146">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-147">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-148">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-149">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-14a">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-14b">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-14c">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-157">
         <name>DL_ADC12_setClockConfig</name>
         <value>0x651</value>
         <object_component_ref idref="oc-ec"/>
      </symbol>
      <symbol id="sm-160">
         <name>DL_Common_delayCycles</name>
         <value>0xcd1</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-16a">
         <name>DL_DMA_initChannel</name>
         <value>0x5b9</value>
         <object_component_ref idref="oc-115"/>
      </symbol>
      <symbol id="sm-17a">
         <name>DL_Timer_setClockConfig</name>
         <value>0xaa5</value>
         <object_component_ref idref="oc-e0"/>
      </symbol>
      <symbol id="sm-17b">
         <name>DL_Timer_initTimerMode</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-186">
         <name>_c_int00_noargs</name>
         <value>0x931</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-187">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-193">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x691</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-19b">
         <name>_system_pre_init</name>
         <value>0xcf7</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-1a6">
         <name>__TI_zero_init</name>
         <value>0xc99</value>
         <object_component_ref idref="oc-52"/>
      </symbol>
      <symbol id="sm-1af">
         <name>__TI_decompress_none</name>
         <value>0xc75</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-1ba">
         <name>__TI_decompress_lzss</name>
         <value>0x431</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-1c4">
         <name>abort</name>
         <value>0xce5</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-1ce">
         <name>HOSTexit</name>
         <value>0xcef</value>
         <object_component_ref idref="oc-bc"/>
      </symbol>
      <symbol id="sm-1cf">
         <name>C$$EXIT</name>
         <value>0xcee</value>
         <object_component_ref idref="oc-bc"/>
      </symbol>
      <symbol id="sm-1d5">
         <name>__aeabi_memcpy</name>
         <value>0xcdd</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-1d6">
         <name>__aeabi_memcpy4</name>
         <value>0xcdd</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-1d7">
         <name>__aeabi_memcpy8</name>
         <value>0xcdd</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-1de">
         <name>__aeabi_memclr</name>
         <value>0xcc5</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-1df">
         <name>__aeabi_memclr4</name>
         <value>0xcc5</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-1e0">
         <name>__aeabi_memclr8</name>
         <value>0xcc5</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-1e9">
         <name>TI_memcpy_small</name>
         <value>0xc63</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-1f2">
         <name>TI_memset_small</name>
         <value>0xca9</value>
         <object_component_ref idref="oc-ac"/>
      </symbol>
      <symbol id="sm-1f3">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1f6">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1f7">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
