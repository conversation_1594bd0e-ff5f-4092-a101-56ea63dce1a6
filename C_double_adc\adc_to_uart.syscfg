/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4150"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12  = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121 = ADC12.addInstance();
const ADC122 = ADC12.addInstance();
const Board  = scripting.addModule("/ti/driverlib/Board");
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
ADC121.$name                      = "ADC12_0";
ADC121.sampClkDiv                 = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.adcMem0chansel             = "DL_ADC12_INPUT_CHAN_2";
ADC121.sampleTime0                = "125 us";
ADC121.enabledInterrupts          = ["DL_ADC12_INTERRUPT_MEM0_RESULT_LOADED"];
ADC121.powerDownMode              = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.repeatMode                 = true;
ADC121.trigSrc                    = "DL_ADC12_TRIG_SRC_EVENT";
ADC121.configureDMA               = true;
ADC121.subChanID                  = 1;
ADC121.peripheral.$assign         = "ADC0";
ADC121.peripheral.adcPin2.$assign = "PA25";
ADC121.adcPin2Config.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
ADC121.DMA_CHANNEL.$name          = "DMA_CH0";

ADC122.$name               = "ADC12_1";
ADC122.trigSrc             = "DL_ADC12_TRIG_SRC_EVENT";
ADC122.subChanID           = 2;
ADC122.repeatMode          = true;
ADC122.sampClkDiv          = "DL_ADC12_CLOCK_DIVIDE_8";
ADC122.powerDownMode       = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC122.sampleTime0         = "125us";
ADC122.enabledInterrupts   = ["DL_ADC12_INTERRUPT_MEM0_RESULT_LOADED"];
ADC122.adcPin0Config.$name = "ti_driverlib_gpio_GPIOPinGeneric2";


GPIO1.$name                         = "GPIO_LEDS";
GPIO1.port                          = "PORTA";
GPIO1.portSegment                   = "Lower";
GPIO1.associatedPins[0].$name       = "USER_LED_1";
GPIO1.associatedPins[0].assignedPin = "0";
GPIO1.associatedPins[0].pin.$assign = "PA0";

SYSCTL.forceDefaultClkConfig = true;

TIMER1.$name                       = "TIMER_0";
TIMER1.timerMode                   = "PERIODIC";
TIMER1.event1PublisherChannel      = 1;
TIMER1.event2PublisherChannel      = 2;
TIMER1.event1ControllerInterruptEn = ["ZERO_EVENT"];
TIMER1.event2ControllerInterruptEn = ["ZERO_EVENT"];
TIMER1.timerPeriod                 = "100us";
TIMER1.counterZero                 = true;

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
ADC121.DMA_CHANNEL.peripheral.$suggestSolution = "DMA_CH0";
ADC122.peripheral.$suggestSolution             = "ADC1";
ADC122.peripheral.adcPin0.$suggestSolution     = "PA15";
Board.peripheral.$suggestSolution              = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution     = "PA20";
Board.peripheral.swdioPin.$suggestSolution     = "PA19";
SYSCTL.peripheral.$suggestSolution             = "SYSCTL";
TIMER1.peripheral.$suggestSolution             = "TIMA0";
