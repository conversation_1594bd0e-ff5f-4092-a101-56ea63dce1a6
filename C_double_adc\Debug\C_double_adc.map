******************************************************************************
            TI ARM Clang Linker PC v4.0.2                      
******************************************************************************
>> Linked Tue Jul 15 20:19:17 2025

OUTPUT FILE NAME:   <C_double_adc.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000079d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000bb0  0001f450  R  X
  SRAM                  20200000   00008000  000012c2  00006d3e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000bb0   00000bb0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000a80   00000a80    r-x .text
  00000b40    00000b40    00000040   00000040    r-- .rodata
  00000b80    00000b80    00000030   00000030    r-- .cinit
20200000    20200000    000010c4   00000000    rw-
  20200000    20200000    000010be   00000000    rw- .bss
  202010c0    202010c0    00000004   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000a80     
                  000000c0    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000001a8    000000c4     adc_to_uart.o (.text.main)
                  0000026c    00000088     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  000002f4    0000007c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_1_init)
                  00000370    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000003ec    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000454    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  000004ac    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  000004f8    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00000544    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000058e    00000002     --HOLE-- [fill = 0]
                  00000590    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000005d0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000060c    00000038     ti_msp_dl_config.o (.text.DL_Timer_setPublisherChanID)
                  00000644    00000030     adc_to_uart.o (.text.DL_ADC12_getMemResult)
                  00000674    0000002c     ti_msp_dl_config.o (.text.DL_ADC12_setDMASamplesCnt)
                  000006a0    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000006cc    0000002c     adc_to_uart.o (.text.__NVIC_EnableIRQ)
                  000006f8    0000002a     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000722    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000074a    00000002     --HOLE-- [fill = 0]
                  0000074c    00000028     ti_msp_dl_config.o (.text.DL_Timer_enableEvent)
                  00000774    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000079c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000007c4    00000024     adc_to_uart.o (.text.ADC1_IRQHandler)
                  000007e8    00000020     adc_to_uart.o (.text.ADC0_IRQHandler)
                  00000808    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00000826    00000002     --HOLE-- [fill = 0]
                  00000828    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_clearInterruptStatus)
                  00000844    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMA)
                  00000860    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableInterrupt)
                  0000087c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  00000898    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000008b4    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000008d0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000008ec    0000001a     ti_msp_dl_config.o (.text.DL_ADC12_setSubscriberChanID)
                  00000906    00000002     --HOLE-- [fill = 0]
                  00000908    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00000920    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00000938    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00000950    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00000968    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00000980    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00000998    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000009b0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000009c8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000009e0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000009f8    00000018     adc_to_uart.o (.text.DL_Timer_startCounter)
                  00000a10    00000018     adc_to_uart.o (.text.DL_Timer_stopCounter)
                  00000a28    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  00000a40    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00000a56    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00000a6a    00000002     --HOLE-- [fill = 0]
                  00000a6c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00000a80    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00000a94    00000012     adc_to_uart.o (.text.DL_ADC12_getPendingInterrupt)
                  00000aa6    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00000ab8    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00000aca    00000002     --HOLE-- [fill = 0]
                  00000acc    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00000adc    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00000aec    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00000afa    00000002     --HOLE-- [fill = 0]
                  00000afc    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00000b08    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00000b12    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00000b1a    00000002     --HOLE-- [fill = 0]
                  00000b1c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000b24    00000006     libc.a : exit.c.obj (.text:abort)
                  00000b2a    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000b2e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000b32    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000b36    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000b3a    00000006     --HOLE-- [fill = 0]

.cinit     0    00000b80    00000030     
                  00000b80    0000000c     (__TI_handler_table)
                  00000b8c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000b94    00000007     (.cinit..data.load) [load image, compression = lzss]
                  00000b9b    00000001     --HOLE-- [fill = 0]
                  00000b9c    00000010     (__TI_cinit_table)
                  00000bac    00000004     --HOLE-- [fill = 0]

.rodata    0    00000b40    00000040     
                  00000b40    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00000b58    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00000b6c    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00000b74    00000008     ti_msp_dl_config.o (.rodata.gADC12_1ClockConfig)
                  00000b7c    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00000b7f    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000010be     UNINITIALIZED
                  20200000    00000800     (.common:gAdcWaveform0)
                  20200800    00000800     (.common:gAdcWaveform1)
                  20201000    000000bc     (.common:gTIMER_0Backup)
                  202010bc    00000001     (.common:gCheckADC0_Done)
                  202010bd    00000001     (.common:gCheckADC1_Done)

.data      0    202010c0    00000004     UNINITIALIZED
                  202010c0    00000004     adc_to_uart.o (.data.gSampleIndex)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       adc_to_uart.o                  422    0         4102   
       ti_msp_dl_config.o             1502   63        188    
       startup_mspm0g350x_ticlang.o   8      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         1932   255       4290   
                                                              
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     260    0         0      
       dl_dma.o                       76     0         0      
       dl_adc12.o                     64     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         410    0         0      
                                                              
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       copy_zero_init.c.obj           16     0         0      
       memset16.S.obj                 14     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         300    0         0      
                                                              
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memset.S.obj             12     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         20     0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      43        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   2666   298       4802   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00000b9c records: 2, size/record: 8, table size: 16
	.bss: load addr=00000b8c, load size=00000008 bytes, run addr=20200000, run size=000010be bytes, compression=zero_init
	.data: load addr=00000b94, load size=00000007 bytes, run addr=202010c0, run size=00000004 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000b80 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
000007e9  ADC0_IRQHandler               
000007c5  ADC1_IRQHandler               
00000b2b  AES_IRQHandler                
00000b2e  C$$EXIT                       
00000b2b  CANFD0_IRQHandler             
00000b2b  DAC0_IRQHandler               
00000591  DL_ADC12_setClockConfig       
00000b09  DL_Common_delayCycles         
000004f9  DL_DMA_initChannel            
000000c1  DL_Timer_initTimerMode        
000008d1  DL_Timer_setClockConfig       
00000b2b  DMA_IRQHandler                
00000b2b  Default_Handler               
00000b2b  GROUP0_IRQHandler             
00000b2b  GROUP1_IRQHandler             
00000b2f  HOSTexit                      
00000b2b  HardFault_Handler             
00000b2b  I2C0_IRQHandler               
00000b2b  I2C1_IRQHandler               
00000b2b  NMI_Handler                   
00000b2b  PendSV_Handler                
00000b2b  RTC_IRQHandler                
00000b33  Reset_Handler                 
00000b2b  SPI0_IRQHandler               
00000b2b  SPI1_IRQHandler               
00000b2b  SVC_Handler                   
0000026d  SYSCFG_DL_ADC12_0_init        
000002f5  SYSCFG_DL_ADC12_1_init        
00000a29  SYSCFG_DL_DMA_CH0_init        
00000b13  SYSCFG_DL_DMA_init            
00000775  SYSCFG_DL_GPIO_init           
000006f9  SYSCFG_DL_SYSCTL_init         
00000455  SYSCFG_DL_TIMER_0_init        
000006a1  SYSCFG_DL_init                
000003ed  SYSCFG_DL_initPower           
00000b2b  SysTick_Handler               
00000b2b  TIMA0_IRQHandler              
00000b2b  TIMA1_IRQHandler              
00000b2b  TIMG0_IRQHandler              
00000b2b  TIMG12_IRQHandler             
00000b2b  TIMG6_IRQHandler              
00000b2b  TIMG7_IRQHandler              
00000b2b  TIMG8_IRQHandler              
00000aa7  TI_memcpy_small               
00000aed  TI_memset_small               
00000b2b  UART0_IRQHandler              
00000b2b  UART1_IRQHandler              
00000b2b  UART2_IRQHandler              
00000b2b  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000b9c  __TI_CINIT_Base               
00000bac  __TI_CINIT_Limit              
00000bac  __TI_CINIT_Warm               
00000b80  __TI_Handler_Table_Base       
00000b8c  __TI_Handler_Table_Limit      
000005d1  __TI_auto_init_nobinit_nopinit
00000371  __TI_decompress_lzss          
00000ab9  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000add  __TI_zero_init                
00000afd  __aeabi_memclr                
00000afd  __aeabi_memclr4               
00000afd  __aeabi_memclr8               
00000b1d  __aeabi_memcpy                
00000b1d  __aeabi_memcpy4               
00000b1d  __aeabi_memcpy8               
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
0000079d  _c_int00_noargs               
UNDEFED   _system_post_cinit            
00000b37  _system_pre_init              
00000b25  abort                         
ffffffff  binit                         
20200000  gAdcWaveform0                 
20200800  gAdcWaveform1                 
202010bc  gCheckADC0_Done               
202010bd  gCheckADC1_Done               
202010c0  gSampleIndex                  
20201000  gTIMER_0Backup                
00000000  interruptVectors              
000001a9  main                          


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  DL_Timer_initTimerMode        
000001a9  main                          
00000200  __STACK_SIZE                  
0000026d  SYSCFG_DL_ADC12_0_init        
000002f5  SYSCFG_DL_ADC12_1_init        
00000371  __TI_decompress_lzss          
000003ed  SYSCFG_DL_initPower           
00000455  SYSCFG_DL_TIMER_0_init        
000004f9  DL_DMA_initChannel            
00000591  DL_ADC12_setClockConfig       
000005d1  __TI_auto_init_nobinit_nopinit
000006a1  SYSCFG_DL_init                
000006f9  SYSCFG_DL_SYSCTL_init         
00000775  SYSCFG_DL_GPIO_init           
0000079d  _c_int00_noargs               
000007c5  ADC1_IRQHandler               
000007e9  ADC0_IRQHandler               
000008d1  DL_Timer_setClockConfig       
00000a29  SYSCFG_DL_DMA_CH0_init        
00000aa7  TI_memcpy_small               
00000ab9  __TI_decompress_none          
00000add  __TI_zero_init                
00000aed  TI_memset_small               
00000afd  __aeabi_memclr                
00000afd  __aeabi_memclr4               
00000afd  __aeabi_memclr8               
00000b09  DL_Common_delayCycles         
00000b13  SYSCFG_DL_DMA_init            
00000b1d  __aeabi_memcpy                
00000b1d  __aeabi_memcpy4               
00000b1d  __aeabi_memcpy8               
00000b25  abort                         
00000b2b  AES_IRQHandler                
00000b2b  CANFD0_IRQHandler             
00000b2b  DAC0_IRQHandler               
00000b2b  DMA_IRQHandler                
00000b2b  Default_Handler               
00000b2b  GROUP0_IRQHandler             
00000b2b  GROUP1_IRQHandler             
00000b2b  HardFault_Handler             
00000b2b  I2C0_IRQHandler               
00000b2b  I2C1_IRQHandler               
00000b2b  NMI_Handler                   
00000b2b  PendSV_Handler                
00000b2b  RTC_IRQHandler                
00000b2b  SPI0_IRQHandler               
00000b2b  SPI1_IRQHandler               
00000b2b  SVC_Handler                   
00000b2b  SysTick_Handler               
00000b2b  TIMA0_IRQHandler              
00000b2b  TIMA1_IRQHandler              
00000b2b  TIMG0_IRQHandler              
00000b2b  TIMG12_IRQHandler             
00000b2b  TIMG6_IRQHandler              
00000b2b  TIMG7_IRQHandler              
00000b2b  TIMG8_IRQHandler              
00000b2b  UART0_IRQHandler              
00000b2b  UART1_IRQHandler              
00000b2b  UART2_IRQHandler              
00000b2b  UART3_IRQHandler              
00000b2e  C$$EXIT                       
00000b2f  HOSTexit                      
00000b33  Reset_Handler                 
00000b37  _system_pre_init              
00000b80  __TI_Handler_Table_Base       
00000b8c  __TI_Handler_Table_Limit      
00000b9c  __TI_CINIT_Base               
00000bac  __TI_CINIT_Limit              
00000bac  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  gAdcWaveform0                 
20200800  gAdcWaveform1                 
20201000  gTIMER_0Backup                
202010bc  gCheckADC0_Done               
202010bd  gCheckADC1_Done               
202010c0  gSampleIndex                  
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[99 symbols]
