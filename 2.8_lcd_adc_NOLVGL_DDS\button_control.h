#ifndef BUTTON_CONTROL_H_
#define BUTTON_CONTROL_H_

#include <stdint.h>
#include <stdbool.h>

// 按键控制参数
#define FREQ_STEP_HZ        1000    // 频率步进：1kHz
#define FREQ_MIN_HZ         1000    // 最小频率：1kHz
#define FREQ_MAX_HZ         50000   // 最大频率：50kHz
#define DEBOUNCE_DELAY_MS   200     // 按键防抖延时（增加到200ms）

// 按键状态结构
typedef struct {
    uint32_t current_freq;          // 当前频率
    uint32_t last_button_time;      // 上次按键时间（用于防抖）
    bool freq_changed;              // 频率是否发生变化
} button_control_t;

// 按键控制函数
void button_control_init(button_control_t* ctrl, uint32_t initial_freq);
void button_control_freq_up(button_control_t* ctrl);
void button_control_freq_down(button_control_t* ctrl);
uint32_t button_control_get_freq(button_control_t* ctrl);
bool button_control_freq_changed(button_control_t* ctrl);
void button_control_clear_change_flag(button_control_t* ctrl);

// 新增：轮询方式按键检测（更稳定）
void button_control_poll(button_control_t* ctrl);

// 中断服务函数声明（备用）- 已移到empty.c中
// void GPIOB_IRQHandler(void);  // 已废弃，使用GROUP1_IRQHandler

#endif /* BUTTON_CONTROL_H_ */
