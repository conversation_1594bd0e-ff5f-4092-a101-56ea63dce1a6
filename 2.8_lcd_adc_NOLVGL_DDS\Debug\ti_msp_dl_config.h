/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3507

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define GPIO_HFXT_PORT                                                     GPIOA
#define GPIO_HFXIN_PIN                                             DL_GPIO_PIN_5
#define GPIO_HFXIN_IOMUX                                         (IOMUX_PINCM10)
#define GPIO_HFXOUT_PIN                                            DL_GPIO_PIN_6
#define GPIO_HFXOUT_IOMUX                                        (IOMUX_PINCM11)
#define CPUCLK_FREQ                                                     32000000



/* Defines for TIMER_0 */
#define TIMER_0_INST                                                     (TIMA0)
#define TIMER_0_INST_IRQHandler                                 TIMA0_IRQHandler
#define TIMER_0_INST_INT_IRQN                                   (TIMA0_INT_IRQn)
#define TIMER_0_INST_LOAD_VALUE                                           (799U)
#define TIMER_0_INST_PUB_0_CH                                                (1)
#define TIMER_0_INST_PUB_1_CH                                                (2)



/* Defines for Console */
#define Console_INST                                                       UART0
#define Console_INST_FREQUENCY                                          32000000
#define Console_INST_IRQHandler                                 UART0_IRQHandler
#define Console_INST_INT_IRQN                                     UART0_INT_IRQn
#define GPIO_Console_RX_PORT                                               GPIOA
#define GPIO_Console_TX_PORT                                               GPIOA
#define GPIO_Console_RX_PIN                                        DL_GPIO_PIN_1
#define GPIO_Console_TX_PIN                                        DL_GPIO_PIN_0
#define GPIO_Console_IOMUX_RX                                     (IOMUX_PINCM2)
#define GPIO_Console_IOMUX_TX                                     (IOMUX_PINCM1)
#define GPIO_Console_IOMUX_RX_FUNC                      IOMUX_PINCM2_PF_UART0_RX
#define GPIO_Console_IOMUX_TX_FUNC                      IOMUX_PINCM1_PF_UART0_TX
#define Console_BAUD_RATE                                               (115200)
#define Console_IBRD_32_MHZ_115200_BAUD                                     (17)
#define Console_FBRD_32_MHZ_115200_BAUD                                     (23)




/* Defines for TFTspi */
#define TFTspi_INST                                                        SPI1
#define TFTspi_INST_IRQHandler                                  SPI1_IRQHandler
#define TFTspi_INST_INT_IRQN                                      SPI1_INT_IRQn
#define GPIO_TFTspi_PICO_PORT                                             GPIOB
#define GPIO_TFTspi_PICO_PIN                                      DL_GPIO_PIN_8
#define GPIO_TFTspi_IOMUX_PICO                                  (IOMUX_PINCM25)
#define GPIO_TFTspi_IOMUX_PICO_FUNC                  IOMUX_PINCM25_PF_SPI1_PICO
#define GPIO_TFTspi_POCI_PORT                                             GPIOB
#define GPIO_TFTspi_POCI_PIN                                      DL_GPIO_PIN_7
#define GPIO_TFTspi_IOMUX_POCI                                  (IOMUX_PINCM24)
#define GPIO_TFTspi_IOMUX_POCI_FUNC                  IOMUX_PINCM24_PF_SPI1_POCI
/* GPIO configuration for TFTspi */
#define GPIO_TFTspi_SCLK_PORT                                             GPIOB
#define GPIO_TFTspi_SCLK_PIN                                      DL_GPIO_PIN_9
#define GPIO_TFTspi_IOMUX_SCLK                                  (IOMUX_PINCM26)
#define GPIO_TFTspi_IOMUX_SCLK_FUNC                  IOMUX_PINCM26_PF_SPI1_SCLK



/* Defines for ADC12_0 */
#define ADC12_0_INST                                                        ADC0
#define ADC12_0_INST_IRQHandler                                  ADC0_IRQHandler
#define ADC12_0_INST_INT_IRQN                                    (ADC0_INT_IRQn)
#define ADC12_0_ADCMEM_0                                      DL_ADC12_MEM_IDX_0
#define ADC12_0_ADCMEM_0_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_0_ADCMEM_0_REF_VOLTAGE_V                                       3.3
#define ADC12_0_INST_SUB_CH                                                  (2)
#define GPIO_ADC12_0_C0_PORT                                               GPIOA
#define GPIO_ADC12_0_C0_PIN                                       DL_GPIO_PIN_27

/* Defines for ADC12_1 */
#define ADC12_1_INST                                                        ADC1
#define ADC12_1_INST_IRQHandler                                  ADC1_IRQHandler
#define ADC12_1_INST_INT_IRQN                                    (ADC1_INT_IRQn)
#define ADC12_1_ADCMEM_0                                      DL_ADC12_MEM_IDX_0
#define ADC12_1_ADCMEM_0_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_1_ADCMEM_0_REF_VOLTAGE_V                                       3.3
#define ADC12_1_INST_SUB_CH                                                  (1)
#define GPIO_ADC12_1_C0_PORT                                               GPIOA
#define GPIO_ADC12_1_C0_PIN                                       DL_GPIO_PIN_15



/* Defines for DMA_CH0 */
#define DMA_CH0_CHAN_ID                                                      (1)
#define ADC12_0_INST_DMA_TRIGGER                      (DMA_ADC0_EVT_GEN_BD_TRIG)
/* Defines for DMA_CH1 */
#define DMA_CH1_CHAN_ID                                                      (0)
#define ADC12_1_INST_DMA_TRIGGER                      (DMA_ADC1_EVT_GEN_BD_TRIG)


/* Port definition for Pin Group LED */
#define LED_PORT                                                         (GPIOB)

/* Defines for PIN: GPIOB.22 with pinCMx 50 on package pin 21 */
#define LED_PIN_PIN                                             (DL_GPIO_PIN_22)
#define LED_PIN_IOMUX                                            (IOMUX_PINCM50)
/* Port definition for Pin Group LCD */
#define LCD_PORT                                                         (GPIOB)

/* Defines for RES: GPIOB.10 with pinCMx 27 on package pin 62 */
#define LCD_RES_PIN                                             (DL_GPIO_PIN_10)
#define LCD_RES_IOMUX                                            (IOMUX_PINCM27)
/* Defines for DC: GPIOB.11 with pinCMx 28 on package pin 63 */
#define LCD_DC_PIN                                              (DL_GPIO_PIN_11)
#define LCD_DC_IOMUX                                             (IOMUX_PINCM28)
/* Defines for CS: GPIOB.14 with pinCMx 31 on package pin 2 */
#define LCD_CS_PIN                                              (DL_GPIO_PIN_14)
#define LCD_CS_IOMUX                                             (IOMUX_PINCM31)
/* Defines for BLK: GPIOB.26 with pinCMx 57 on package pin 28 */
#define LCD_BLK_PIN                                             (DL_GPIO_PIN_26)
#define LCD_BLK_IOMUX                                            (IOMUX_PINCM57)
/* Defines for P_W_CLK: GPIOB.13 with pinCMx 30 on package pin 1 */
#define AD_9850PINS_P_W_CLK_PORT                                         (GPIOB)
#define AD_9850PINS_P_W_CLK_PIN                                 (DL_GPIO_PIN_13)
#define AD_9850PINS_P_W_CLK_IOMUX                                (IOMUX_PINCM30)
/* Defines for DATA: GPIOB.15 with pinCMx 32 on package pin 3 */
#define AD_9850PINS_DATA_PORT                                            (GPIOB)
#define AD_9850PINS_DATA_PIN                                    (DL_GPIO_PIN_15)
#define AD_9850PINS_DATA_IOMUX                                   (IOMUX_PINCM32)
/* Defines for FQ_UD: GPIOB.16 with pinCMx 33 on package pin 4 */
#define AD_9850PINS_FQ_UD_PORT                                           (GPIOB)
#define AD_9850PINS_FQ_UD_PIN                                   (DL_GPIO_PIN_16)
#define AD_9850PINS_FQ_UD_IOMUX                                  (IOMUX_PINCM33)
/* Defines for RESET: GPIOA.12 with pinCMx 34 on package pin 5 */
#define AD_9850PINS_RESET_PORT                                           (GPIOA)
#define AD_9850PINS_RESET_PIN                                   (DL_GPIO_PIN_12)
#define AD_9850PINS_RESET_IOMUX                                  (IOMUX_PINCM34)
/* Port definition for Pin Group BUTTONS */
#define BUTTONS_PORT                                                     (GPIOB)

/* Defines for FREQ_UP: GPIOB.0 with pinCMx 12 on package pin 47 */
// pins affected by this interrupt request:["FREQ_UP","FREQ_DOWN"]
#define BUTTONS_INT_IRQN                                        (GPIOB_INT_IRQn)
#define BUTTONS_INT_IIDX                        (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define BUTTONS_FREQ_UP_IIDX                                 (DL_GPIO_IIDX_DIO0)
#define BUTTONS_FREQ_UP_PIN                                      (DL_GPIO_PIN_0)
#define BUTTONS_FREQ_UP_IOMUX                                    (IOMUX_PINCM12)
/* Defines for FREQ_DOWN: GPIOB.1 with pinCMx 13 on package pin 48 */
#define BUTTONS_FREQ_DOWN_IIDX                               (DL_GPIO_IIDX_DIO1)
#define BUTTONS_FREQ_DOWN_PIN                                    (DL_GPIO_PIN_1)
#define BUTTONS_FREQ_DOWN_IOMUX                                  (IOMUX_PINCM13)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_SYSCTL_CLK_init(void);
void SYSCFG_DL_TIMER_0_init(void);
void SYSCFG_DL_Console_init(void);
void SYSCFG_DL_TFTspi_init(void);
void SYSCFG_DL_ADC12_0_init(void);
void SYSCFG_DL_ADC12_1_init(void);
void SYSCFG_DL_DMA_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
