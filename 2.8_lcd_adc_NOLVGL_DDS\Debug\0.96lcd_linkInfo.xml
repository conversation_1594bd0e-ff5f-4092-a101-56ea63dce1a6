<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker Unix v4.0.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/bin/tiarmlnk -I/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib -o 0.96lcd.out -m0.96lcd.map -i/home/<USER>/ti/mspm0_sdk_2_04_00_06/source -i/home/<USER>/workspace_ccstheia/0.96lcd -i/home/<USER>/workspace_ccstheia/0.96lcd/Debug/syscfg -i/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=0.96lcd_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./BSP/LCD/lcd.o ./BSP/LCD/lcd_init.o ./Board/board.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x687f4a1a</link_time>
   <link_errors>0x0</link_errors>
   <output_file>/home/<USER>/workspace_ccstheia/0.96lcd/Debug/0.96lcd.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x2049</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd/Debug/./</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd/Debug/./</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd/Debug/./</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd/Debug/./BSP/LCD/</path>
         <kind>object</kind>
         <file>lcd.o</file>
         <name>lcd.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd/Debug/./BSP/LCD/</path>
         <kind>object</kind>
         <file>lcd_init.o</file>
         <name>lcd_init.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd/Debug/./Board/</path>
         <kind>object</kind>
         <file>board.o</file>
         <name>board.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd/Debug/</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-14">
         <path>/home/<USER>/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>/home/<USER>/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_spi.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>/home/<USER>/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>/home/<USER>/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_sysctl_mspm0g1x0x_g3x0x.o</name>
      </input_file>
      <input_file id="fl-2e">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>vsnprintf.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-3b">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-3c">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fputs.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_io_perm.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>setvbuf.c.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fopen.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fseek.c.obj</name>
      </input_file>
      <input_file id="fl-4d">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fclose.c.obj</name>
      </input_file>
      <input_file id="fl-ed">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-ee">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-ef">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-f0">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>remove.c.obj</name>
      </input_file>
      <input_file id="fl-f1">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>open.c.obj</name>
      </input_file>
      <input_file id="fl-f2">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>lseek.c.obj</name>
      </input_file>
      <input_file id="fl-f3">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>close.c.obj</name>
      </input_file>
      <input_file id="fl-f4">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>unlink.c.obj</name>
      </input_file>
      <input_file id="fl-f5">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-f6">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-f7">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-f8">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-f9">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-fa">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-fb">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-fc">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-fd">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-fe">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-ff">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-100">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-101">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-102">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-103">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-104">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-105">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-106">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-107">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-108">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-109">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-10a">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-10b">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-10c">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-10d">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-10e">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-10f">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-110">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>getdevice.c.obj</name>
      </input_file>
      <input_file id="fl-111">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-112">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
      <input_file id="fl-113">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strcmp-armv6m.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-84">
         <name>.text.LCD_Init</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x14c2</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x1582</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1582</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.text.LCD_Fill</name>
         <load_address>0x1584</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1584</run_address>
         <size>0x2c0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.text.LCD_Address_Set</name>
         <load_address>0x1844</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1844</run_address>
         <size>0x234</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text.main</name>
         <load_address>0x1a78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a78</run_address>
         <size>0x124</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.text.DL_SYSCTL_configSYSPLL</name>
         <load_address>0x1b9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b9c</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.text.LCD_WR_DATA</name>
         <load_address>0x1c78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c78</run_address>
         <size>0x70</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-df">
         <name>.text.DL_SYSCTL_setHFCLKSourceHFXTParams</name>
         <load_address>0x1ce8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ce8</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x1d4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1d4c</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x1db0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1db0</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x1e10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e10</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-be">
         <name>.text.SYSCFG_DL_SPI_LCD_init</name>
         <load_address>0x1e60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1e60</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text.DL_UART_init</name>
         <load_address>0x1eac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1eac</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.text.DL_SPI_init</name>
         <load_address>0x1ef4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ef4</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <load_address>0x1f38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f38</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x1f7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1f7c</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x1fbc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1fbc</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.text.SYSCFG_DL_SYSCTL_CLK_init</name>
         <load_address>0x1ff8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ff8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x2020</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2020</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x2048</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2048</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-46">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x2070</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2070</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.text.DL_SPI_setClockConfig</name>
         <load_address>0x2086</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2086</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x2098</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2098</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-90">
         <name>.text.delay_ms</name>
         <load_address>0x20ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20ac</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x20bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20bc</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-99">
         <name>.text:abort</name>
         <load_address>0x20c6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20c6</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text.HOSTexit</name>
         <load_address>0x20cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20cc</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-ed"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x20d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20d0</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.text._system_pre_init</name>
         <load_address>0x20d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x20d4</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.cinit..bss.load</name>
         <load_address>0x2118</load_address>
         <readonly>true</readonly>
         <run_address>0x2118</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-14a">
         <name>__TI_handler_table</name>
         <load_address>0x2120</load_address>
         <readonly>true</readonly>
         <run_address>0x2120</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-14b">
         <name>__TI_cinit_table</name>
         <load_address>0x2124</load_address>
         <readonly>true</readonly>
         <run_address>0x2124</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-e7">
         <name>.rodata.gSYSPLLConfig</name>
         <load_address>0x20d8</load_address>
         <readonly>true</readonly>
         <run_address>0x20d8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.rodata.gSPI_LCD_config</name>
         <load_address>0x2100</load_address>
         <readonly>true</readonly>
         <run_address>0x2100</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x210a</load_address>
         <readonly>true</readonly>
         <run_address>0x210a</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.rodata.gSPI_LCD_clockConfig</name>
         <load_address>0x2114</load_address>
         <readonly>true</readonly>
         <run_address>0x2114</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x2116</load_address>
         <readonly>true</readonly>
         <run_address>0x2116</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-114">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-c0">
         <name>.common:gSPI_LCDBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_loc</name>
         <load_address>0x178</load_address>
         <run_address>0x178</run_address>
         <size>0x2bb2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_loc</name>
         <load_address>0x2d2a</load_address>
         <run_address>0x2d2a</run_address>
         <size>0x252d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_loc</name>
         <load_address>0x5257</load_address>
         <run_address>0x5257</run_address>
         <size>0x260</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_loc</name>
         <load_address>0x54b7</load_address>
         <run_address>0x54b7</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_loc</name>
         <load_address>0x54ca</load_address>
         <run_address>0x54ca</run_address>
         <size>0x816</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_loc</name>
         <load_address>0x5ce0</load_address>
         <run_address>0x5ce0</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_loc</name>
         <load_address>0x649c</load_address>
         <run_address>0x649c</run_address>
         <size>0x414</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_loc</name>
         <load_address>0x68b0</load_address>
         <run_address>0x68b0</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_loc</name>
         <load_address>0x6988</load_address>
         <run_address>0x6988</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_loc</name>
         <load_address>0x6dac</load_address>
         <run_address>0x6dac</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_loc</name>
         <load_address>0x6f18</load_address>
         <run_address>0x6f18</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x89</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.debug_abbrev</name>
         <load_address>0x89</load_address>
         <run_address>0x89</run_address>
         <size>0x221</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_abbrev</name>
         <load_address>0x2aa</load_address>
         <run_address>0x2aa</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_abbrev</name>
         <load_address>0x317</load_address>
         <run_address>0x317</run_address>
         <size>0x213</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.debug_abbrev</name>
         <load_address>0x52a</load_address>
         <run_address>0x52a</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_abbrev</name>
         <load_address>0x6d2</load_address>
         <run_address>0x6d2</run_address>
         <size>0x232</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_abbrev</name>
         <load_address>0x904</load_address>
         <run_address>0x904</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_abbrev</name>
         <load_address>0x966</load_address>
         <run_address>0x966</run_address>
         <size>0x277</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-107">
         <name>.debug_abbrev</name>
         <load_address>0xbdd</load_address>
         <run_address>0xbdd</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-105">
         <name>.debug_abbrev</name>
         <load_address>0xe78</load_address>
         <run_address>0xe78</run_address>
         <size>0x218</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_abbrev</name>
         <load_address>0x1090</load_address>
         <run_address>0x1090</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_abbrev</name>
         <load_address>0x113f</load_address>
         <run_address>0x113f</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_abbrev</name>
         <load_address>0x12af</load_address>
         <run_address>0x12af</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_abbrev</name>
         <load_address>0x12e8</load_address>
         <run_address>0x12e8</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_abbrev</name>
         <load_address>0x13aa</load_address>
         <run_address>0x13aa</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_abbrev</name>
         <load_address>0x1442</load_address>
         <run_address>0x1442</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ed"/>
      </object_component>
      <object_component id="oc-150">
         <name>.debug_abbrev</name>
         <load_address>0x146e</load_address>
         <run_address>0x146e</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x17f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_info</name>
         <load_address>0x17f</load_address>
         <run_address>0x17f</run_address>
         <size>0x289d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x2a1c</load_address>
         <run_address>0x2a1c</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_info</name>
         <load_address>0x2a9c</load_address>
         <run_address>0x2a9c</run_address>
         <size>0x178b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_info</name>
         <load_address>0x4227</load_address>
         <run_address>0x4227</run_address>
         <size>0x7d9f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_info</name>
         <load_address>0xbfc6</load_address>
         <run_address>0xbfc6</run_address>
         <size>0xaa5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_info</name>
         <load_address>0xca6b</load_address>
         <run_address>0xca6b</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_info</name>
         <load_address>0xcae0</load_address>
         <run_address>0xcae0</run_address>
         <size>0x1142</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_info</name>
         <load_address>0xdc22</load_address>
         <run_address>0xdc22</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_info</name>
         <load_address>0xeec8</load_address>
         <run_address>0xeec8</run_address>
         <size>0x1090</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xff58</load_address>
         <run_address>0xff58</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_info</name>
         <load_address>0x1037b</load_address>
         <run_address>0x1037b</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_info</name>
         <load_address>0x10abf</load_address>
         <run_address>0x10abf</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_info</name>
         <load_address>0x10b05</load_address>
         <run_address>0x10b05</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_info</name>
         <load_address>0x10c97</load_address>
         <run_address>0x10c97</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_info</name>
         <load_address>0x10d8f</load_address>
         <run_address>0x10d8f</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ed"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.debug_info</name>
         <load_address>0x10dca</load_address>
         <run_address>0x10dca</run_address>
         <size>0xa2</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x12c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_str</name>
         <load_address>0x12c</load_address>
         <run_address>0x12c</run_address>
         <size>0x237c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_str</name>
         <load_address>0x24a8</load_address>
         <run_address>0x24a8</run_address>
         <size>0x160</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_str</name>
         <load_address>0x2608</load_address>
         <run_address>0x2608</run_address>
         <size>0x3b7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_str</name>
         <load_address>0x29bf</load_address>
         <run_address>0x29bf</run_address>
         <size>0x608</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_str</name>
         <load_address>0x2fc7</load_address>
         <run_address>0x2fc7</run_address>
         <size>0x48f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_str</name>
         <load_address>0x3456</load_address>
         <run_address>0x3456</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_str</name>
         <load_address>0x35cd</load_address>
         <run_address>0x35cd</run_address>
         <size>0xc45</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-108">
         <name>.debug_str</name>
         <load_address>0x4212</load_address>
         <run_address>0x4212</run_address>
         <size>0xced</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-106">
         <name>.debug_str</name>
         <load_address>0x4eff</load_address>
         <run_address>0x4eff</run_address>
         <size>0x107f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_str</name>
         <load_address>0x5f7e</load_address>
         <run_address>0x5f7e</run_address>
         <size>0x22d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_str</name>
         <load_address>0x61ab</load_address>
         <run_address>0x61ab</run_address>
         <size>0x337</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_str</name>
         <load_address>0x64e2</load_address>
         <run_address>0x64e2</run_address>
         <size>0xfd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_str</name>
         <load_address>0x65df</load_address>
         <run_address>0x65df</run_address>
         <size>0x1a3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_str</name>
         <load_address>0x6782</load_address>
         <run_address>0x6782</run_address>
         <size>0x150</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_str</name>
         <load_address>0x68d2</load_address>
         <run_address>0x68d2</run_address>
         <size>0xf1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ed"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_frame</name>
         <load_address>0x24</load_address>
         <run_address>0x24</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_frame</name>
         <load_address>0x130</load_address>
         <run_address>0x130</run_address>
         <size>0x204</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_frame</name>
         <load_address>0x334</load_address>
         <run_address>0x334</run_address>
         <size>0xb0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_frame</name>
         <load_address>0x3e4</load_address>
         <run_address>0x3e4</run_address>
         <size>0xc8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_frame</name>
         <load_address>0x4ac</load_address>
         <run_address>0x4ac</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_frame</name>
         <load_address>0x4cc</load_address>
         <run_address>0x4cc</run_address>
         <size>0x234</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_frame</name>
         <load_address>0x700</load_address>
         <run_address>0x700</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_frame</name>
         <load_address>0x8b8</load_address>
         <run_address>0x8b8</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_frame</name>
         <load_address>0x9e4</load_address>
         <run_address>0x9e4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_frame</name>
         <load_address>0xa74</load_address>
         <run_address>0xa74</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_frame</name>
         <load_address>0xb74</load_address>
         <run_address>0xb74</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_frame</name>
         <load_address>0xb94</load_address>
         <run_address>0xb94</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_frame</name>
         <load_address>0xbcc</load_address>
         <run_address>0xbcc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_frame</name>
         <load_address>0xbfc</load_address>
         <run_address>0xbfc</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ed"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xcf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_line</name>
         <load_address>0xcf</load_address>
         <run_address>0xcf</run_address>
         <size>0x709</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_line</name>
         <load_address>0x7d8</load_address>
         <run_address>0x7d8</run_address>
         <size>0xc1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_line</name>
         <load_address>0x899</load_address>
         <run_address>0x899</run_address>
         <size>0x136f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_line</name>
         <load_address>0x1c08</load_address>
         <run_address>0x1c08</run_address>
         <size>0x3c86</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_line</name>
         <load_address>0x588e</load_address>
         <run_address>0x588e</run_address>
         <size>0x52c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0x5dba</load_address>
         <run_address>0x5dba</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.debug_line</name>
         <load_address>0x5f33</load_address>
         <run_address>0x5f33</run_address>
         <size>0xc1b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_line</name>
         <load_address>0x6b4e</load_address>
         <run_address>0x6b4e</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_line</name>
         <load_address>0x7566</load_address>
         <run_address>0x7566</run_address>
         <size>0x983</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x7ee9</load_address>
         <run_address>0x7ee9</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_line</name>
         <load_address>0x80c5</load_address>
         <run_address>0x80c5</run_address>
         <size>0x403</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_line</name>
         <load_address>0x84c8</load_address>
         <run_address>0x84c8</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_line</name>
         <load_address>0x8506</load_address>
         <run_address>0x8506</run_address>
         <size>0x106</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_line</name>
         <load_address>0x860c</load_address>
         <run_address>0x860c</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_line</name>
         <load_address>0x8673</load_address>
         <run_address>0x8673</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ed"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x70</load_address>
         <run_address>0x70</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_ranges</name>
         <load_address>0x88</load_address>
         <run_address>0x88</run_address>
         <size>0x150</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_ranges</name>
         <load_address>0x1d8</load_address>
         <run_address>0x1d8</run_address>
         <size>0x1cc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_ranges</name>
         <load_address>0x1e98</load_address>
         <run_address>0x1e98</run_address>
         <size>0xd0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_ranges</name>
         <load_address>0x1f68</load_address>
         <run_address>0x1f68</run_address>
         <size>0x390</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_ranges</name>
         <load_address>0x22f8</load_address>
         <run_address>0x22f8</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_ranges</name>
         <load_address>0x24a0</load_address>
         <run_address>0x24a0</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_ranges</name>
         <load_address>0x2648</load_address>
         <run_address>0x2648</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_ranges</name>
         <load_address>0x2690</load_address>
         <run_address>0x2690</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_ranges</name>
         <load_address>0x26d8</load_address>
         <run_address>0x26d8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_ranges</name>
         <load_address>0x26f0</load_address>
         <run_address>0x26f0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x2018</size>
         <contents>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-c8"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-5f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x2118</load_address>
         <run_address>0x2118</run_address>
         <size>0x18</size>
         <contents>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-14b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x20d8</load_address>
         <run_address>0x20d8</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-ef"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-114"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x28</size>
         <contents>
            <object_component_ref idref="oc-c0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-14e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10b" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10c" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10d" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10e" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10f" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-110" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-112" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-12e" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6f3e</size>
         <contents>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-9f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-130" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x147d</size>
         <contents>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-150"/>
         </contents>
      </logical_group>
      <logical_group id="lg-132" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x10e6c</size>
         <contents>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-14f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-134" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x69c3</size>
         <contents>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-fb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-136" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc1c</size>
         <contents>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-d1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-138" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x86b4</size>
         <contents>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-d0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-13a" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2708</size>
         <contents>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-9b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-14d" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-153" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2130</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-154" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x28</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-155" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x2130</used_space>
         <unused_space>0x1ded0</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x2018</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20d8</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2118</start_address>
               <size>0x18</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x2130</start_address>
               <size>0x1ded0</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x228</used_space>
         <unused_space>0x7dd8</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-110"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-112"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x28</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200028</start_address>
               <size>0x7dd8</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x2118</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x28</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x2124</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x212c</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x212c</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x2120</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x2124</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3b">
         <name>main</name>
         <value>0x1a79</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-62">
         <name>SYSCFG_DL_init</name>
         <value>0x2021</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-63">
         <name>SYSCFG_DL_initPower</name>
         <value>0x1f7d</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-64">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x1e11</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-65">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x1d4d</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-66">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x1db1</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-67">
         <name>SYSCFG_DL_SPI_LCD_init</name>
         <value>0x1e61</value>
         <object_component_ref idref="oc-be"/>
      </symbol>
      <symbol id="sm-68">
         <name>SYSCFG_DL_SYSCTL_CLK_init</name>
         <value>0x1ff9</value>
         <object_component_ref idref="oc-bf"/>
      </symbol>
      <symbol id="sm-69">
         <name>gSPI_LCDBackup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-74">
         <name>Default_Handler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-75">
         <name>Reset_Handler</name>
         <value>0x20d1</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-76">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-77">
         <name>NMI_Handler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-78">
         <name>HardFault_Handler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-79">
         <name>SVC_Handler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7a">
         <name>PendSV_Handler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7b">
         <name>SysTick_Handler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7c">
         <name>GROUP0_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>GROUP1_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>TIMG8_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7f">
         <name>UART3_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-80">
         <name>ADC0_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>ADC1_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>CANFD0_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>DAC0_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>SPI0_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-85">
         <name>SPI1_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-86">
         <name>UART1_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>UART2_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>UART0_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>TIMG0_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>TIMG6_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>TIMA0_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8c">
         <name>TIMA1_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>TIMG7_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>TIMG12_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8f">
         <name>I2C0_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-90">
         <name>I2C1_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-91">
         <name>AES_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-92">
         <name>RTC_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-93">
         <name>DMA_IRQHandler</name>
         <value>0x1583</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9e">
         <name>LCD_Fill</name>
         <value>0x1585</value>
         <object_component_ref idref="oc-8a"/>
      </symbol>
      <symbol id="sm-10c">
         <name>LCD_WR_DATA</name>
         <value>0x1c79</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-10d">
         <name>LCD_Address_Set</name>
         <value>0x1845</value>
         <object_component_ref idref="oc-c8"/>
      </symbol>
      <symbol id="sm-10e">
         <name>LCD_Init</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-84"/>
      </symbol>
      <symbol id="sm-119">
         <name>delay_ms</name>
         <value>0x20ad</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
      <symbol id="sm-11a">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-11b">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-11c">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-11d">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-11e">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-11f">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-120">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-121">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-122">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-12b">
         <name>DL_Common_delayCycles</name>
         <value>0x20bd</value>
         <object_component_ref idref="oc-80"/>
      </symbol>
      <symbol id="sm-138">
         <name>DL_SPI_init</name>
         <value>0x1ef5</value>
         <object_component_ref idref="oc-f7"/>
      </symbol>
      <symbol id="sm-139">
         <name>DL_SPI_setClockConfig</name>
         <value>0x2087</value>
         <object_component_ref idref="oc-f1"/>
      </symbol>
      <symbol id="sm-146">
         <name>DL_UART_init</name>
         <value>0x1ead</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-147">
         <name>DL_UART_setClockConfig</name>
         <value>0x2099</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-158">
         <name>DL_SYSCTL_configSYSPLL</name>
         <value>0x1b9d</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-159">
         <name>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <value>0x1f39</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-15a">
         <name>DL_SYSCTL_setHFCLKSourceHFXTParams</name>
         <value>0x1ce9</value>
         <object_component_ref idref="oc-df"/>
      </symbol>
      <symbol id="sm-167">
         <name>_c_int00_noargs</name>
         <value>0x2049</value>
         <object_component_ref idref="oc-4f"/>
      </symbol>
      <symbol id="sm-168">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-174">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x1fbd</value>
         <object_component_ref idref="oc-a0"/>
      </symbol>
      <symbol id="sm-17c">
         <name>_system_pre_init</name>
         <value>0x20d5</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-187">
         <name>__TI_zero_init_nomemset</name>
         <value>0x2071</value>
         <object_component_ref idref="oc-46"/>
      </symbol>
      <symbol id="sm-197">
         <name>abort</name>
         <value>0x20c7</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-1af">
         <name>HOSTexit</name>
         <value>0x20cd</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-1b0">
         <name>C$$EXIT</name>
         <value>0x20cc</value>
         <object_component_ref idref="oc-ce"/>
      </symbol>
      <symbol id="sm-1c3">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c5">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c6">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
