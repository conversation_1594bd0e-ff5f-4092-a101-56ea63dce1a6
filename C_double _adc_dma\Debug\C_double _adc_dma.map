******************************************************************************
            TI ARM Clang Linker PC v4.0.2                      
******************************************************************************
>> Linked Wed Jul 16 08:32:00 2025

OUTPUT FILE NAME:   <C_double _adc_dma.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000931


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000d88  0001f278  R  X
  SRAM                  20200000   00008000  000012be  00006d42  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000d88   00000d88    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000c40   00000c40    r-x .text
  00000d00    00000d00    00000058   00000058    r-- .rodata
  00000d58    00000d58    00000030   00000030    r-- .cinit
20200000    20200000    000010be   00000000    rw-
  20200000    20200000    000010bc   00000000    rw- .bss
  202010bc    202010bc    00000002   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000c40     
                  000000c0    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000001a8    000000c0     adc_to_uart.o (.text.main)
                  00000268    000000a4     adc_to_uart.o (.text.reconfigure_and_restart_dma)
                  0000030c    00000094     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_1_init)
                  000003a0    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00000430    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000004ac    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000514    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  0000056c    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  000005b8    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00000604    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000064e    00000002     --HOLE-- [fill = 0]
                  00000650    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00000690    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000006cc    00000038     ti_msp_dl_config.o (.text.DL_Timer_setPublisherChanID)
                  00000704    00000030     adc_to_uart.o (.text.DL_DMA_setTransferSize)
                  00000734    00000030     ti_msp_dl_config.o (.text.DL_DMA_setTransferSize)
                  00000764    0000002c     ti_msp_dl_config.o (.text.DL_ADC12_setDMASamplesCnt)
                  00000790    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH1_init)
                  000007bc    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000007e8    0000002c     adc_to_uart.o (.text.__NVIC_EnableIRQ)
                  00000814    0000002a     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000083e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00000866    00000002     --HOLE-- [fill = 0]
                  00000868    00000028     adc_to_uart.o (.text.DL_DMA_setDestAddr)
                  00000890    00000028     adc_to_uart.o (.text.DL_DMA_setSrcAddr)
                  000008b8    00000028     ti_msp_dl_config.o (.text.DL_Timer_enableEvent)
                  000008e0    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  00000908    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000930    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000958    00000026     adc_to_uart.o (.text.DL_DMA_enableChannel)
                  0000097e    00000002     --HOLE-- [fill = 0]
                  00000980    00000020     adc_to_uart.o (.text.ADC0_IRQHandler)
                  000009a0    00000020     adc_to_uart.o (.text.ADC1_IRQHandler)
                  000009c0    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  000009de    00000002     --HOLE-- [fill = 0]
                  000009e0    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_clearInterruptStatus)
                  000009fc    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMA)
                  00000a18    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMATrigger)
                  00000a34    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableInterrupt)
                  00000a50    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  00000a6c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00000a88    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00000aa4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00000ac0    0000001a     ti_msp_dl_config.o (.text.DL_ADC12_setSubscriberChanID)
                  00000ada    00000002     --HOLE-- [fill = 0]
                  00000adc    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00000af4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00000b0c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00000b24    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00000b3c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00000b54    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00000b6c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00000b84    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00000b9c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00000bb4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00000bcc    00000018     adc_to_uart.o (.text.DL_Timer_startCounter)
                  00000be4    00000018     adc_to_uart.o (.text.DL_Timer_stopCounter)
                  00000bfc    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00000c12    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00000c26    00000002     --HOLE-- [fill = 0]
                  00000c28    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00000c3c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00000c50    00000012     adc_to_uart.o (.text.DL_ADC12_getPendingInterrupt)
                  00000c62    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00000c74    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00000c86    00000002     --HOLE-- [fill = 0]
                  00000c88    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00000c98    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00000ca8    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00000cb6    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00000cc2    00000002     --HOLE-- [fill = 0]
                  00000cc4    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00000cd0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00000cda    00000002     --HOLE-- [fill = 0]
                  00000cdc    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000ce4    00000006     libc.a : exit.c.obj (.text:abort)
                  00000cea    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000cee    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000cf2    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000cf6    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000cfa    00000006     --HOLE-- [fill = 0]

.cinit     0    00000d58    00000030     
                  00000d58    0000000c     (__TI_handler_table)
                  00000d64    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000d6c    00000006     (.cinit..data.load) [load image, compression = lzss]
                  00000d72    00000002     --HOLE-- [fill = 0]
                  00000d74    00000010     (__TI_cinit_table)
                  00000d84    00000004     --HOLE-- [fill = 0]

.rodata    0    00000d00    00000058     
                  00000d00    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00000d18    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH1Config)
                  00000d30    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00000d44    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00000d4c    00000008     ti_msp_dl_config.o (.rodata.gADC12_1ClockConfig)
                  00000d54    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00000d57    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000010bc     UNINITIALIZED
                  20200000    00000800     (.common:gADC0_Data)
                  20200800    00000800     (.common:gADC1_Data)
                  20201000    000000bc     (.common:gTIMER_0Backup)

.data      0    202010bc    00000002     UNINITIALIZED
                  202010bc    00000001     adc_to_uart.o (.data.gADC0_DMADone)
                  202010bd    00000001     adc_to_uart.o (.data.gADC1_DMADone)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       adc_to_uart.o                  696    0         4098   
       ti_msp_dl_config.o             1674   87        188    
       startup_mspm0g350x_ticlang.o   8      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         2378   279       4286   
                                                              
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     260    0         0      
       dl_dma.o                       76     0         0      
       dl_adc12.o                     64     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         410    0         0      
                                                              
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       copy_zero_init.c.obj           16     0         0      
       memset16.S.obj                 14     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         300    0         0      
                                                              
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_memset.S.obj             12     0         0      
       aeabi_memcpy.S.obj             8      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         20     0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      42        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   3112   321       4798   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00000d74 records: 2, size/record: 8, table size: 16
	.bss: load addr=00000d64, load size=00000008 bytes, run addr=20200000, run size=000010bc bytes, compression=zero_init
	.data: load addr=00000d6c, load size=00000006 bytes, run addr=202010bc, run size=00000002 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000d58 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
00000981  ADC0_IRQHandler               
000009a1  ADC1_IRQHandler               
00000ceb  AES_IRQHandler                
00000cee  C$$EXIT                       
00000ceb  CANFD0_IRQHandler             
00000ceb  DAC0_IRQHandler               
00000651  DL_ADC12_setClockConfig       
00000cd1  DL_Common_delayCycles         
000005b9  DL_DMA_initChannel            
000000c1  DL_Timer_initTimerMode        
00000aa5  DL_Timer_setClockConfig       
00000ceb  DMA_IRQHandler                
00000ceb  Default_Handler               
00000ceb  GROUP0_IRQHandler             
00000ceb  GROUP1_IRQHandler             
00000cef  HOSTexit                      
00000ceb  HardFault_Handler             
00000ceb  I2C0_IRQHandler               
00000ceb  I2C1_IRQHandler               
00000ceb  NMI_Handler                   
00000ceb  PendSV_Handler                
00000ceb  RTC_IRQHandler                
00000cf3  Reset_Handler                 
00000ceb  SPI0_IRQHandler               
00000ceb  SPI1_IRQHandler               
00000ceb  SVC_Handler                   
000003a1  SYSCFG_DL_ADC12_0_init        
0000030d  SYSCFG_DL_ADC12_1_init        
000008e1  SYSCFG_DL_DMA_CH0_init        
00000791  SYSCFG_DL_DMA_CH1_init        
00000cb7  SYSCFG_DL_DMA_init            
00000909  SYSCFG_DL_GPIO_init           
00000815  SYSCFG_DL_SYSCTL_init         
00000515  SYSCFG_DL_TIMER_0_init        
000007bd  SYSCFG_DL_init                
000004ad  SYSCFG_DL_initPower           
00000ceb  SysTick_Handler               
00000ceb  TIMA0_IRQHandler              
00000ceb  TIMA1_IRQHandler              
00000ceb  TIMG0_IRQHandler              
00000ceb  TIMG12_IRQHandler             
00000ceb  TIMG6_IRQHandler              
00000ceb  TIMG7_IRQHandler              
00000ceb  TIMG8_IRQHandler              
00000c63  TI_memcpy_small               
00000ca9  TI_memset_small               
00000ceb  UART0_IRQHandler              
00000ceb  UART1_IRQHandler              
00000ceb  UART2_IRQHandler              
00000ceb  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000d74  __TI_CINIT_Base               
00000d84  __TI_CINIT_Limit              
00000d84  __TI_CINIT_Warm               
00000d58  __TI_Handler_Table_Base       
00000d64  __TI_Handler_Table_Limit      
00000691  __TI_auto_init_nobinit_nopinit
00000431  __TI_decompress_lzss          
00000c75  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000c99  __TI_zero_init                
00000cc5  __aeabi_memclr                
00000cc5  __aeabi_memclr4               
00000cc5  __aeabi_memclr8               
00000cdd  __aeabi_memcpy                
00000cdd  __aeabi_memcpy4               
00000cdd  __aeabi_memcpy8               
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000931  _c_int00_noargs               
UNDEFED   _system_post_cinit            
00000cf7  _system_pre_init              
00000ce5  abort                         
ffffffff  binit                         
20200000  gADC0_Data                    
20200800  gADC1_Data                    
20201000  gTIMER_0Backup                
00000000  interruptVectors              
000001a9  main                          
00000269  reconfigure_and_restart_dma   


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  DL_Timer_initTimerMode        
000001a9  main                          
00000200  __STACK_SIZE                  
00000269  reconfigure_and_restart_dma   
0000030d  SYSCFG_DL_ADC12_1_init        
000003a1  SYSCFG_DL_ADC12_0_init        
00000431  __TI_decompress_lzss          
000004ad  SYSCFG_DL_initPower           
00000515  SYSCFG_DL_TIMER_0_init        
000005b9  DL_DMA_initChannel            
00000651  DL_ADC12_setClockConfig       
00000691  __TI_auto_init_nobinit_nopinit
00000791  SYSCFG_DL_DMA_CH1_init        
000007bd  SYSCFG_DL_init                
00000815  SYSCFG_DL_SYSCTL_init         
000008e1  SYSCFG_DL_DMA_CH0_init        
00000909  SYSCFG_DL_GPIO_init           
00000931  _c_int00_noargs               
00000981  ADC0_IRQHandler               
000009a1  ADC1_IRQHandler               
00000aa5  DL_Timer_setClockConfig       
00000c63  TI_memcpy_small               
00000c75  __TI_decompress_none          
00000c99  __TI_zero_init                
00000ca9  TI_memset_small               
00000cb7  SYSCFG_DL_DMA_init            
00000cc5  __aeabi_memclr                
00000cc5  __aeabi_memclr4               
00000cc5  __aeabi_memclr8               
00000cd1  DL_Common_delayCycles         
00000cdd  __aeabi_memcpy                
00000cdd  __aeabi_memcpy4               
00000cdd  __aeabi_memcpy8               
00000ce5  abort                         
00000ceb  AES_IRQHandler                
00000ceb  CANFD0_IRQHandler             
00000ceb  DAC0_IRQHandler               
00000ceb  DMA_IRQHandler                
00000ceb  Default_Handler               
00000ceb  GROUP0_IRQHandler             
00000ceb  GROUP1_IRQHandler             
00000ceb  HardFault_Handler             
00000ceb  I2C0_IRQHandler               
00000ceb  I2C1_IRQHandler               
00000ceb  NMI_Handler                   
00000ceb  PendSV_Handler                
00000ceb  RTC_IRQHandler                
00000ceb  SPI0_IRQHandler               
00000ceb  SPI1_IRQHandler               
00000ceb  SVC_Handler                   
00000ceb  SysTick_Handler               
00000ceb  TIMA0_IRQHandler              
00000ceb  TIMA1_IRQHandler              
00000ceb  TIMG0_IRQHandler              
00000ceb  TIMG12_IRQHandler             
00000ceb  TIMG6_IRQHandler              
00000ceb  TIMG7_IRQHandler              
00000ceb  TIMG8_IRQHandler              
00000ceb  UART0_IRQHandler              
00000ceb  UART1_IRQHandler              
00000ceb  UART2_IRQHandler              
00000ceb  UART3_IRQHandler              
00000cee  C$$EXIT                       
00000cef  HOSTexit                      
00000cf3  Reset_Handler                 
00000cf7  _system_pre_init              
00000d58  __TI_Handler_Table_Base       
00000d64  __TI_Handler_Table_Limit      
00000d74  __TI_CINIT_Base               
00000d84  __TI_CINIT_Limit              
00000d84  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  gADC0_Data                    
20200800  gADC1_Data                    
20201000  gTIMER_0Backup                
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[98 symbols]
