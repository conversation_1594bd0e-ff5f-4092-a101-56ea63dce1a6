******************************************************************************
            TI ARM Clang Linker Unix v4.0.2                    
******************************************************************************
>> Linked Wed Jul 23 16:15:58 2025

OUTPUT FILE NAME:   <2.8_lcd_work.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000484d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00008518  00017ae8  R  X
  SRAM                  20200000   00008000  0000022c  00007dd4  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00008518   00008518    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000048e8   000048e8    r-x .text
  000049a8    000049a8    00003b40   00003b40    r-- .rodata
  000084e8    000084e8    00000030   00000030    r-- .cinit
20200000    20200000    0000002c   00000000    rw-
  20200000    20200000    00000028   00000000    rw- .bss
  20200028    20200028    00000004   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000048e8     
                  000000c0    00000faa     lcd_init.o (.text.LCD_Init)
                  0000106a    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000106c    00000644     libc.a : k_rem_pio2.c.obj (.text.__kernel_rem_pio2)
                  000016b0    0000045c     empty.o (.text.main)
                  00001b0c    00000458     libc.a : s_sin.c.obj (.text.sin)
                  00001f64    00000400     lcd.o (.text.LCD_ShowChar)
                  00002364    000002c0     lcd.o (.text.LCD_Fill)
                  00002624    0000027c     lcd.o (.text.LCD_ShowChinese12x12)
                  000028a0    0000027c     lcd.o (.text.LCD_ShowChinese16x16)
                  00002b1c    0000027c     lcd.o (.text.LCD_ShowChinese24x24)
                  00002d98    0000027c     lcd.o (.text.LCD_ShowChinese32x32)
                  00003014    00000234     lcd_init.o (.text.LCD_Address_Set)
                  00003248    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000033da    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000033dc    00000168     libc.a : k_sin.c.obj (.text.__kernel_sin)
                  00003544    00000150            : k_cos.c.obj (.text.__kernel_cos)
                  00003694    00000144            : s_floor.c.obj (.text.floor)
                  000037d8    00000134     lcd.o (.text.LCD_ShowFloatNum1)
                  0000390c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003a18    0000010a     lcd.o (.text.LCD_ShowIntNum)
                  00003b22    00000002     --HOLE-- [fill = 0]
                  00003b24    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00003c08    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00003ce4    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00003dbc    000000b8     wave.o (.text.LCD_DrawGraph)
                  00003e74    000000b2     lcd.o (.text.LCD_ShowChinese)
                  00003f26    000000a4     lcd.o (.text.LCD_DrawLine)
                  00003fca    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00004064    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000040f0    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000416c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000041e0    00000070     lcd_init.o (.text.LCD_WR_DATA)
                  00004250    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000042b8    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  0000431c    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00004380    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000043e2    00000002     --HOLE-- [fill = 0]
                  000043e4    00000060     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00004444    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000449a    00000002     --HOLE-- [fill = 0]
                  0000449c    00000050     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000044ec    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_LCD_init)
                  00004538    0000004a     lcd.o (.text.LCD_ShowString)
                  00004582    00000002     --HOLE-- [fill = 0]
                  00004584    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000045ce    00000002     --HOLE-- [fill = 0]
                  000045d0    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00004618    00000044                 : dl_spi.o (.text.DL_SPI_init)
                  0000465c    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000046a0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000046e0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004720    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000475c    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00004796    00000002     --HOLE-- [fill = 0]
                  00004798    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000047d0    0000002c                            : floatsidf.S.obj (.text.__floatsidf)
                  000047fc    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_CLK_init)
                  00004824    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000484c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00004874    00000016            : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000488a    00000002     --HOLE-- [fill = 0]
                  0000488c    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  0000489c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000048b2    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  000048c4    00000012                 : dl_uart.o (.text.DL_UART_setClockConfig)
                  000048d6    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  000048e8    00000010            : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_2)
                  000048f8    00000010     board.o (.text.delay_ms)
                  00004908    0000000e     libc.a : s_sin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00004916    00000002     --HOLE-- [fill = 0]
                  00004918    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00004928    0000000e     libc.a : s_sin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00004936    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00004940    0000000a     libc.a : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000494a    00000002     --HOLE-- [fill = 0]
                  0000494c    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004954    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0000495c    00000006     libc.a : k_cos.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004962    00000002     --HOLE-- [fill = 0]
                  00004964    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00004974    00000006     libc.a : k_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000497a    00000006            : s_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00004980    00000006            : s_sin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00004986    00000006            : s_sin.c.obj (.text.OUTLINED_FUNCTION_6)
                  0000498c    00000006            : exit.c.obj (.text:abort)
                  00004992    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00004996    00000004     libc.a : s_sin.c.obj (.text.OUTLINED_FUNCTION_3)
                  0000499a    00000004            : s_sin.c.obj (.text.OUTLINED_FUNCTION_5)
                  0000499e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000049a2    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000049a6    00000002     --HOLE-- [fill = 0]

.cinit     0    000084e8    00000030     
                  000084e8    0000000c     (__TI_handler_table)
                  000084f4    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000084fc    00000007     (.cinit..data.load) [load image, compression = lzss]
                  00008503    00000001     --HOLE-- [fill = 0]
                  00008504    00000010     (__TI_cinit_table)
                  00008514    00000004     --HOLE-- [fill = 0]

.rodata    0    000049a8    00003b40     
                  000049a8    000017c0     lcd.o (.rodata.ascii_3216)
                  00006168    000011d0     lcd.o (.rodata.ascii_2412)
                  00007338    000005f0     lcd.o (.rodata.ascii_1608)
                  00007928    00000474     lcd.o (.rodata.ascii_1206)
                  00007d9c    0000028a     lcd.o (.rodata.tfont32)
                  00008026    00000172     lcd.o (.rodata.tfont24)
                  00008198    00000108     libc.a : k_rem_pio2.c.obj (.rodata.ipio2)
                  000082a0    000000cc     lcd.o (.rodata.tfont16)
                  0000836c    000000bd     lcd.o (.rodata.tfont12)
                  00008429    00000007     empty.o (.rodata.str1.14685083708502177989.1)
                  00008430    00000040     libc.a : k_rem_pio2.c.obj (.rodata.PIo2)
                  00008470    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00008498    00000013     empty.o (.rodata.str1.9517790425240694019.1)
                  000084ab    00000001     --HOLE-- [fill = 0]
                  000084ac    00000010     libc.a : k_rem_pio2.c.obj (.rodata.cst16)
                  000084bc    0000000a     ti_msp_dl_config.o (.rodata.gSPI_LCD_config)
                  000084c6    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  000084d0    00000007     empty.o (.rodata.str1.254342170260855183.1)
                  000084d7    00000002     ti_msp_dl_config.o (.rodata.gSPI_LCD_clockConfig)
                  000084d9    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  000084db    00000002     empty.o (.rodata.str1.11898133897667081452.1)
                  000084dd    00000002     empty.o (.rodata.str1.16704889451495720520.1)
                  000084df    00000002     empty.o (.rodata.str1.17669528882079347314.1)
                  000084e1    00000002     empty.o (.rodata.str1.7401042497206923953.1)
                  000084e3    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000028     UNINITIALIZED
                  20200000    00000028     (.common:gSPI_LCDBackup)

.data      0    20200028    00000004     UNINITIALIZED
                  20200028    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    ./
       empty.o                        1116    41        0      
       ti_msp_dl_config.o             496     64        40     
       startup_mspm0g350x_ticlang.o   6       192       0      
       wave.o                         184     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1802    297       40     
                                                               
    ./BSP/LCD/
       lcd.o                          5262    14713     0      
       lcd_init.o                     4686    0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         9948    14713     0      
                                                               
    ./Board/
       board.o                        16      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         16      0         0      
                                                               
    /home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/libc.a
       k_rem_pio2.c.obj               1652    344       0      
       s_sin.c.obj                    1166    0         0      
       k_sin.c.obj                    366     0         0      
       k_cos.c.obj                    342     0         0      
       s_floor.c.obj                  324     0         0      
       s_scalbn.c.obj                 216     0         0      
       memcpy16.S.obj                 154     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4502    344       4      
                                                               
    /home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    /home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       mulsf3.S.obj                   140     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1796    0         0      
                                                               
    /home/<USER>/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_uart.o                      90      0         0      
       dl_spi.o                       86      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         574     0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       43        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   18642   15397     556    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00008504 records: 2, size/record: 8, table size: 16
	.bss: load addr=000084f4, load size=00000008 bytes, run addr=20200000, run size=00000028 bytes, compression=zero_init
	.data: load addr=000084fc, load size=00000007 bytes, run addr=20200028, run size=00000004 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000084e8 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00003253     0000488c     00004888   libc.a : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_0)
                             00004948          : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_1)
                             0000497e          : s_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00004998          : s_sin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00003249     00004918     00004914   libc.a : s_sin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00004934          : s_sin.c.obj (.text.OUTLINED_FUNCTION_2)
                             0000498a          : s_sin.c.obj (.text.OUTLINED_FUNCTION_6)
                             0000499c          : s_sin.c.obj (.text.OUTLINED_FUNCTION_5)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00003b25     00004964     00004960   libc.a : k_cos.c.obj (.text.OUTLINED_FUNCTION_0)
                             00004978          : k_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00004984          : s_sin.c.obj (.text.OUTLINED_FUNCTION_4)

[3 trampolines]
[11 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000106b  ADC0_IRQHandler                      
0000106b  ADC1_IRQHandler                      
0000106b  AES_IRQHandler                       
00004992  C$$EXIT                              
0000106b  CANFD0_IRQHandler                    
0000106b  DAC0_IRQHandler                      
00004937  DL_Common_delayCycles                
00004619  DL_SPI_init                          
000048b3  DL_SPI_setClockConfig                
00003c09  DL_SYSCTL_configSYSPLL               
000042b9  DL_SYSCTL_setHFCLKSourceHFXTParams   
0000465d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000045d1  DL_UART_init                         
000048c5  DL_UART_setClockConfig               
0000106b  DMA_IRQHandler                       
0000106b  Default_Handler                      
0000106b  GROUP0_IRQHandler                    
0000106b  GROUP1_IRQHandler                    
00004993  HOSTexit                             
0000106b  HardFault_Handler                    
0000106b  I2C0_IRQHandler                      
0000106b  I2C1_IRQHandler                      
00003015  LCD_Address_Set                      
00003dbd  LCD_DrawGraph                        
00003f27  LCD_DrawLine                         
00002365  LCD_Fill                             
000000c1  LCD_Init                             
00001f65  LCD_ShowChar                         
00003e75  LCD_ShowChinese                      
00002625  LCD_ShowChinese12x12                 
000028a1  LCD_ShowChinese16x16                 
00002b1d  LCD_ShowChinese24x24                 
00002d99  LCD_ShowChinese32x32                 
000037d9  LCD_ShowFloatNum1                    
00003a19  LCD_ShowIntNum                       
00004539  LCD_ShowString                       
000041e1  LCD_WR_DATA                          
0000106b  NMI_Handler                          
0000106b  PendSV_Handler                       
0000106b  RTC_IRQHandler                       
0000499f  Reset_Handler                        
0000106b  SPI0_IRQHandler                      
0000106b  SPI1_IRQHandler                      
0000106b  SVC_Handler                          
0000449d  SYSCFG_DL_GPIO_init                  
000044ed  SYSCFG_DL_SPI_LCD_init               
000047fd  SYSCFG_DL_SYSCTL_CLK_init            
0000431d  SYSCFG_DL_SYSCTL_init                
000043e5  SYSCFG_DL_UART_0_init                
00004825  SYSCFG_DL_init                       
000046a1  SYSCFG_DL_initPower                  
0000106b  SysTick_Handler                      
0000106b  TIMA0_IRQHandler                     
0000106b  TIMA1_IRQHandler                     
0000106b  TIMG0_IRQHandler                     
0000106b  TIMG12_IRQHandler                    
0000106b  TIMG6_IRQHandler                     
0000106b  TIMG7_IRQHandler                     
0000106b  TIMG8_IRQHandler                     
0000106b  UART0_IRQHandler                     
0000106b  UART1_IRQHandler                     
0000106b  UART2_IRQHandler                     
0000106b  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00008504  __TI_CINIT_Base                      
00008514  __TI_CINIT_Limit                     
00008514  __TI_CINIT_Warm                      
000084e8  __TI_Handler_Table_Base              
000084f4  __TI_Handler_Table_Limit             
00004721  __TI_auto_init_nobinit_nopinit       
000040f1  __TI_decompress_lzss                 
000048d7  __TI_decompress_none                 
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000489d  __TI_zero_init_nomemset              
00003253  __adddf3                             
00004585  __aeabi_d2iz                         
00003253  __aeabi_dadd                         
00004381  __aeabi_dcmpeq                       
000043bd  __aeabi_dcmpge                       
000043d1  __aeabi_dcmpgt                       
000043a9  __aeabi_dcmple                       
00004395  __aeabi_dcmplt                       
0000390d  __aeabi_ddiv                         
00003b25  __aeabi_dmul                         
00003249  __aeabi_dsub                         
20200028  __aeabi_errno                        
0000494d  __aeabi_errno_addr                   
00004799  __aeabi_f2iz                         
00004065  __aeabi_fmul                         
000047d1  __aeabi_i2d                          
00004445  __aeabi_idiv                         
000033db  __aeabi_idiv0                        
00004445  __aeabi_idivmod                      
00004955  __aeabi_memcpy                       
00004955  __aeabi_memcpy4                      
00004955  __aeabi_memcpy8                      
000046e1  __aeabi_uidiv                        
000046e1  __aeabi_uidivmod                     
ffffffff  __binit__                            
00004251  __cmpdf2                             
0000390d  __divdf3                             
00004251  __eqdf2                              
00004585  __fixdfsi                            
00004799  __fixsfsi                            
000047d1  __floatsidf                          
0000416d  __gedf2                              
0000416d  __gtdf2                              
00003545  __kernel_cos                         
0000106d  __kernel_rem_pio2                    
000033dd  __kernel_sin                         
00004251  __ledf2                              
00004251  __ltdf2                              
UNDEFED   __mpu_init                           
00003b25  __muldf3                             
0000475d  __muldsi3                            
00004065  __mulsf3                             
00004251  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00003249  __subdf3                             
0000484d  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000049a3  _system_pre_init                     
0000498d  abort                                
00007928  ascii_1206                           
00007338  ascii_1608                           
00006168  ascii_2412                           
000049a8  ascii_3216                           
ffffffff  binit                                
000048f9  delay_ms                             
00003695  floor                                
00003695  floorl                               
20200000  gSPI_LCDBackup                       
00000000  interruptVectors                     
00003ce5  ldexp                                
00003ce5  ldexpl                               
000016b1  main                                 
00003fcb  memcpy                               
00003ce5  scalbn                               
00003ce5  scalbnl                              
00001b0d  sin                                  
00001b0d  sinl                                 
0000836c  tfont12                              
000082a0  tfont16                              
00008026  tfont24                              
00007d9c  tfont32                              


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  LCD_Init                             
00000200  __STACK_SIZE                         
0000106b  ADC0_IRQHandler                      
0000106b  ADC1_IRQHandler                      
0000106b  AES_IRQHandler                       
0000106b  CANFD0_IRQHandler                    
0000106b  DAC0_IRQHandler                      
0000106b  DMA_IRQHandler                       
0000106b  Default_Handler                      
0000106b  GROUP0_IRQHandler                    
0000106b  GROUP1_IRQHandler                    
0000106b  HardFault_Handler                    
0000106b  I2C0_IRQHandler                      
0000106b  I2C1_IRQHandler                      
0000106b  NMI_Handler                          
0000106b  PendSV_Handler                       
0000106b  RTC_IRQHandler                       
0000106b  SPI0_IRQHandler                      
0000106b  SPI1_IRQHandler                      
0000106b  SVC_Handler                          
0000106b  SysTick_Handler                      
0000106b  TIMA0_IRQHandler                     
0000106b  TIMA1_IRQHandler                     
0000106b  TIMG0_IRQHandler                     
0000106b  TIMG12_IRQHandler                    
0000106b  TIMG6_IRQHandler                     
0000106b  TIMG7_IRQHandler                     
0000106b  TIMG8_IRQHandler                     
0000106b  UART0_IRQHandler                     
0000106b  UART1_IRQHandler                     
0000106b  UART2_IRQHandler                     
0000106b  UART3_IRQHandler                     
0000106d  __kernel_rem_pio2                    
000016b1  main                                 
00001b0d  sin                                  
00001b0d  sinl                                 
00001f65  LCD_ShowChar                         
00002365  LCD_Fill                             
00002625  LCD_ShowChinese12x12                 
000028a1  LCD_ShowChinese16x16                 
00002b1d  LCD_ShowChinese24x24                 
00002d99  LCD_ShowChinese32x32                 
00003015  LCD_Address_Set                      
00003249  __aeabi_dsub                         
00003249  __subdf3                             
00003253  __adddf3                             
00003253  __aeabi_dadd                         
000033db  __aeabi_idiv0                        
000033dd  __kernel_sin                         
00003545  __kernel_cos                         
00003695  floor                                
00003695  floorl                               
000037d9  LCD_ShowFloatNum1                    
0000390d  __aeabi_ddiv                         
0000390d  __divdf3                             
00003a19  LCD_ShowIntNum                       
00003b25  __aeabi_dmul                         
00003b25  __muldf3                             
00003c09  DL_SYSCTL_configSYSPLL               
00003ce5  ldexp                                
00003ce5  ldexpl                               
00003ce5  scalbn                               
00003ce5  scalbnl                              
00003dbd  LCD_DrawGraph                        
00003e75  LCD_ShowChinese                      
00003f27  LCD_DrawLine                         
00003fcb  memcpy                               
00004065  __aeabi_fmul                         
00004065  __mulsf3                             
000040f1  __TI_decompress_lzss                 
0000416d  __gedf2                              
0000416d  __gtdf2                              
000041e1  LCD_WR_DATA                          
00004251  __cmpdf2                             
00004251  __eqdf2                              
00004251  __ledf2                              
00004251  __ltdf2                              
00004251  __nedf2                              
000042b9  DL_SYSCTL_setHFCLKSourceHFXTParams   
0000431d  SYSCFG_DL_SYSCTL_init                
00004381  __aeabi_dcmpeq                       
00004395  __aeabi_dcmplt                       
000043a9  __aeabi_dcmple                       
000043bd  __aeabi_dcmpge                       
000043d1  __aeabi_dcmpgt                       
000043e5  SYSCFG_DL_UART_0_init                
00004445  __aeabi_idiv                         
00004445  __aeabi_idivmod                      
0000449d  SYSCFG_DL_GPIO_init                  
000044ed  SYSCFG_DL_SPI_LCD_init               
00004539  LCD_ShowString                       
00004585  __aeabi_d2iz                         
00004585  __fixdfsi                            
000045d1  DL_UART_init                         
00004619  DL_SPI_init                          
0000465d  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000046a1  SYSCFG_DL_initPower                  
000046e1  __aeabi_uidiv                        
000046e1  __aeabi_uidivmod                     
00004721  __TI_auto_init_nobinit_nopinit       
0000475d  __muldsi3                            
00004799  __aeabi_f2iz                         
00004799  __fixsfsi                            
000047d1  __aeabi_i2d                          
000047d1  __floatsidf                          
000047fd  SYSCFG_DL_SYSCTL_CLK_init            
00004825  SYSCFG_DL_init                       
0000484d  _c_int00_noargs                      
0000489d  __TI_zero_init_nomemset              
000048b3  DL_SPI_setClockConfig                
000048c5  DL_UART_setClockConfig               
000048d7  __TI_decompress_none                 
000048f9  delay_ms                             
00004937  DL_Common_delayCycles                
0000494d  __aeabi_errno_addr                   
00004955  __aeabi_memcpy                       
00004955  __aeabi_memcpy4                      
00004955  __aeabi_memcpy8                      
0000498d  abort                                
00004992  C$$EXIT                              
00004993  HOSTexit                             
0000499f  Reset_Handler                        
000049a3  _system_pre_init                     
000049a8  ascii_3216                           
00006168  ascii_2412                           
00007338  ascii_1608                           
00007928  ascii_1206                           
00007d9c  tfont32                              
00008026  tfont24                              
000082a0  tfont16                              
0000836c  tfont12                              
000084e8  __TI_Handler_Table_Base              
000084f4  __TI_Handler_Table_Limit             
00008504  __TI_CINIT_Base                      
00008514  __TI_CINIT_Limit                     
00008514  __TI_CINIT_Warm                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200000  gSPI_LCDBackup                       
20200028  __aeabi_errno                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[162 symbols]
