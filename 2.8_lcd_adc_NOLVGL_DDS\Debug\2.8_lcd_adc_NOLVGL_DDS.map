******************************************************************************
            TI ARM Clang Linker PC v4.0.2                      
******************************************************************************
>> Linked Tue Jul 29 10:19:28 2025

OUTPUT FILE NAME:   <2.8_lcd_adc_NOLVGL_DDS.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0001df85


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  0001e6c8  00001938  R  X
  SRAM                  20200000   00008000  00002486  00005b7a  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    0001e6c8   0001e6c8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00019000   00019000    r-- .rodata
  000190c0    000190c0    000055d0   000055d0    r-x .text
  0001e690    0001e690    00000038   00000038    r-- .cinit
20200000    20200000    00002286   00000000    rw-
  20200000    20200000    00001a5c   00000000    rw- .bss
  20201a5c    20201a5c    0000082a   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000190c0    000055d0     
                  000190c0    0000091c     empty.o (.text.main)
                  000199dc    000008cc     arm_cortexM0l_math.a : arm_cfft_radix8_f32.o (.text.arm_radix8_butterfly_f32)
                  0001a2a8    0000076e                          : arm_cfft_f32.o (.text.arm_cfft_radix8by4_f32)
                  0001aa16    000002b0                          : arm_cfft_f32.o (.text.arm_cfft_radix8by2_f32)
                  0001acc6    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0001acc8    00000260     lcd_init.o (.text.LCD_Init)
                  0001af28    00000214     lcd.o (.text.LCD_ShowChar)
                  0001b13c    000001f0     adc_channel.o (.text.adc_channel_draw_waveform)
                  0001b32c    000001d8     lcd.o (.text.LCD_ShowChinese12x12)
                  0001b504    000001d8     lcd.o (.text.LCD_ShowChinese16x16)
                  0001b6dc    000001d8     lcd.o (.text.LCD_ShowChinese24x24)
                  0001b8b4    000001d8     lcd.o (.text.LCD_ShowChinese32x32)
                  0001ba8c    000001c0     arm_cortexM0l_math.a : arm_cfft_f32.o (.text.arm_cfft_f32)
                  0001bc4c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0001bdde    00000168     arm_cortexM0l_math.a : arm_cmplx_mag_f32.o (.text.arm_cmplx_mag_f32)
                  0001bf46    00000002     --HOLE-- [fill = 0]
                  0001bf48    00000130     adc_channel.o (.text.adc_channel_process_data)
                  0001c078    00000128     lcd.o (.text.LCD_DrawLine)
                  0001c1a0    0000011c     lcd.o (.text.LCD_ShowFloatNum1)
                  0001c2bc    00000116     lcd.o (.text.LCD_ShowChinese)
                  0001c3d2    00000112     arm_cortexM0l_math.a : arm_bitreversal2.o (.text.arm_bitreversal_32)
                  0001c4e4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  0001c5f0    00000108     ad9850.o (.text.dds_set)
                  0001c6f8    000000ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0001c7e4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0001c8cc    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  0001c9b0    000000d8                            : addsf3.S.obj (.text)
                  0001ca88    000000d2     arm_cortexM0l_math.a : arm_rms_f32.o (.text.arm_rms_f32)
                  0001cb5a    000000b4                          : arm_offset_f32.o (.text.arm_offset_f32)
                  0001cc0e    00000002     --HOLE-- [fill = 0]
                  0001cc10    000000b0                          : arm_cfft_init_f32.o (.text.arm_cfft_init_f32)
                  0001ccc0    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_1_init)
                  0001cd60    000000a0     libc.a : e_sqrtf.c.obj (.text.sqrtf)
                  0001ce00    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  0001ce9c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0001cf28    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0001cfb4    00000088     arm_cortexM0l_math.a : arm_mean_f32.o (.text.arm_mean_f32)
                  0001d03c    00000084     lcd.o (.text.LCD_Fill)
                  0001d0c0    00000084     empty.o (.text.__NVIC_SetPriority)
                  0001d144    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0001d1c8    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0001d24a    00000002     --HOLE-- [fill = 0]
                  0001d24c    00000080     adc_channel.o (.text.adc_channel_init)
                  0001d2cc    0000007e     adc_channel.o (.text.adc_channel_display_values)
                  0001d34a    00000002     --HOLE-- [fill = 0]
                  0001d34c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0001d3c8    0000007a     lcd.o (.text.LCD_ShowString)
                  0001d442    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  0001d450    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  0001d4c4    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  0001d528    00000064     lcd_init.o (.text.LCD_Writ_Bus)
                  0001d58c    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0001d5ee    00000002     --HOLE-- [fill = 0]
                  0001d5f0    00000060     empty.o (.text.GROUP1_IRQHandler)
                  0001d650    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  0001d6a8    00000052     lcd_init.o (.text.LCD_Address_Set)
                  0001d6fa    00000002     --HOLE-- [fill = 0]
                  0001d6fc    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  0001d748    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  0001d794    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  0001d7e0    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0001d82a    00000002     --HOLE-- [fill = 0]
                  0001d82c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0001d874    00000044                 : dl_spi.o (.text.DL_SPI_init)
                  0001d8b8    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0001d8fc    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0001d93e    00000002     --HOLE-- [fill = 0]
                  0001d940    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  0001d980    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_Console_init)
                  0001d9c0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TFTspi_init)
                  0001da00    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  0001da40    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  0001da80    0000003c                            : floatsisf.S.obj (.text.__floatsisf)
                  0001dabc    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  0001daf8    0000003c     ad9850.o (.text.dds_databitwrite)
                  0001db34    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0001db70    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0001dbaa    00000002     --HOLE-- [fill = 0]
                  0001dbac    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0001dbe6    00000002     --HOLE-- [fill = 0]
                  0001dbe8    00000038     ti_msp_dl_config.o (.text.DL_Timer_setPublisherChanID)
                  0001dc20    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  0001dc58    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  0001dc8c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0001dcc0    00000034     ad9850.o (.text.dds_reset)
                  0001dcf4    00000030     empty.o (.text.DL_DMA_setTransferSize)
                  0001dd24    00000030     ti_msp_dl_config.o (.text.DL_DMA_setTransferSize)
                  0001dd54    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  0001dd84    00000030     lcd_init.o (.text.LCD_WR_REG)
                  0001ddb4    00000030     lcd.o (.text.mypow)
                  0001dde4    0000002c     ti_msp_dl_config.o (.text.DL_ADC12_setDMASamplesCnt)
                  0001de10    0000002c     lcd.o (.text.LCD_DrawPoint)
                  0001de3c    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH1_init)
                  0001de68    0000002c     empty.o (.text.__NVIC_EnableIRQ)
                  0001de94    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0001debc    00000028     empty.o (.text.DL_DMA_setDestAddr)
                  0001dee4    00000028     empty.o (.text.DL_DMA_setSrcAddr)
                  0001df0c    00000028     ti_msp_dl_config.o (.text.DL_Timer_enableEvent)
                  0001df34    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  0001df5c    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  0001df84    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0001dfac    00000026     empty.o (.text.DL_DMA_enableChannel)
                  0001dfd2    00000002     --HOLE-- [fill = 0]
                  0001dfd4    00000024     empty.o (.text.DL_ADC12_getMemResultAddress)
                  0001dff8    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  0001e01c    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  0001e040    00000020     empty.o (.text.ADC0_IRQHandler)
                  0001e060    00000020     empty.o (.text.ADC1_IRQHandler)
                  0001e080    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  0001e0a0    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  0001e0be    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0001e0dc    0000001e     lcd_init.o (.text.LCD_WR_DATA)
                  0001e0fa    00000002     --HOLE-- [fill = 0]
                  0001e0fc    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_clearInterruptStatus)
                  0001e118    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMA)
                  0001e134    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMATrigger)
                  0001e150    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableInterrupt)
                  0001e16c    0000001c     empty.o (.text.DL_GPIO_clearInterruptStatus)
                  0001e188    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  0001e1a4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0001e1c0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  0001e1dc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  0001e1f8    0000001c     empty.o (.text.DL_Interrupt_getPendingGroup)
                  0001e214    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0001e230    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0001e24c    0000001a     ti_msp_dl_config.o (.text.DL_ADC12_setSubscriberChanID)
                  0001e266    00000002     --HOLE-- [fill = 0]
                  0001e268    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  0001e280    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  0001e298    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  0001e2b0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0001e2c8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  0001e2e0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  0001e2f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  0001e310    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0001e328    00000018     ad9850.o (.text.DL_GPIO_setPins)
                  0001e340    00000018     lcd_init.o (.text.DL_GPIO_setPins)
                  0001e358    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  0001e370    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  0001e388    00000018     lcd_init.o (.text.DL_SPI_isBusy)
                  0001e3a0    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  0001e3b8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0001e3d0    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  0001e3e8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  0001e400    00000018     empty.o (.text.DL_Timer_startCounter)
                  0001e418    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0001e430    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  0001e448    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_CLK_init)
                  0001e460    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0001e476    00000016     lcd_init.o (.text.DL_SPI_transmitData8)
                  0001e48c    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0001e4a2    00000016     board.o (.text.delay_ms)
                  0001e4b8    00000014     ad9850.o (.text.DL_GPIO_clearPins)
                  0001e4cc    00000014     lcd_init.o (.text.DL_GPIO_clearPins)
                  0001e4e0    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0001e4f4    00000014     lcd_init.o (.text.DL_SPI_receiveData8)
                  0001e508    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0001e51c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0001e530    00000014     lcd_init.o (.text.LCD_WR_DATA8)
                  0001e544    00000014     ad9850.o (.text.dds_clkclr)
                  0001e558    00000014     ad9850.o (.text.dds_clkset)
                  0001e56c    00000014     ad9850.o (.text.dds_datclr)
                  0001e580    00000014     ad9850.o (.text.dds_datset)
                  0001e594    00000012     empty.o (.text.DL_ADC12_getPendingInterrupt)
                  0001e5a6    00000012     empty.o (.text.DL_GPIO_getPendingInterrupt)
                  0001e5b8    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  0001e5ca    00000012                 : dl_uart.o (.text.DL_UART_setClockConfig)
                  0001e5dc    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0001e5ee    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0001e600    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  0001e610    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  0001e620    0000000c     ti_msp_dl_config.o (.text.DL_SYSCTL_getClockStatus)
                  0001e62c    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  0001e638    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  0001e644    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0001e64e    0000000a     ad9850.o (.text.dds_clkdelay)
                  0001e658    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0001e660    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  0001e668    00000008     ad9850.o (.text.dds_get_ref_clock)
                  0001e670    00000006     libc.a : exit.c.obj (.text:abort)
                  0001e676    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0001e67a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  0001e67e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0001e682    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0001e686    00000004     ad9850.o (.text.dds_verify_config)
                  0001e68a    00000006     --HOLE-- [fill = 0]

.cinit     0    0001e690    00000038     
                  0001e690    0000000e     (.cinit..data.load) [load image, compression = lzss]
                  0001e69e    00000002     --HOLE-- [fill = 0]
                  0001e6a0    0000000c     (__TI_handler_table)
                  0001e6ac    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0001e6b4    00000010     (__TI_cinit_table)
                  0001e6c4    00000004     --HOLE-- [fill = 0]

.rodata    0    000000c0    00019000     
                  000000c0    00008000     arm_cortexM0l_math.a : arm_common_tables.o (.rodata.twiddleCoef_4096)
                  000080c0    00004000                          : arm_common_tables.o (.rodata.twiddleCoef_2048)
                  0000c0c0    00002000                          : arm_common_tables.o (.rodata.twiddleCoef_1024)
                  0000e0c0    00001f80                          : arm_common_tables.o (.rodata.armBitRevIndexTable4096)
                  00010040    00001dc0                          : arm_common_tables.o (.rodata.armBitRevIndexTable2048)
                  00011e00    000017c0     lcd.o (.rodata.ascii_3216)
                  000135c0    000011d0     lcd.o (.rodata.ascii_2412)
                  00014790    00001000     arm_cortexM0l_math.a : arm_common_tables.o (.rodata.twiddleCoef_512)
                  00015790    00000e10                          : arm_common_tables.o (.rodata.armBitRevIndexTable1024)
                  000165a0    00000800                          : arm_common_tables.o (.rodata.twiddleCoef_256)
                  00016da0    000005f0     lcd.o (.rodata.ascii_1608)
                  00017390    00000474     lcd.o (.rodata.ascii_1206)
                  00017804    00000400     arm_cortexM0l_math.a : arm_common_tables.o (.rodata.twiddleCoef_128)
                  00017c04    00000380                          : arm_common_tables.o (.rodata.armBitRevIndexTable512)
                  00017f84    00000370                          : arm_common_tables.o (.rodata.armBitRevIndexTable256)
                  000182f4    0000028a     lcd.o (.rodata.tfont32)
                  0001857e    00000002     ti_msp_dl_config.o (.rodata.gConsoleClockConfig)
                  00018580    00000200     arm_cortexM0l_math.a : arm_common_tables.o (.rodata.twiddleCoef_64)
                  00018780    000001a0                          : arm_common_tables.o (.rodata.armBitRevIndexTable128)
                  00018920    00000198     lcd.o (.rodata.tfont16)
                  00018ab8    00000172     lcd.o (.rodata.tfont24)
                  00018c2a    00000002     ti_msp_dl_config.o (.rodata.gTFTspi_clockConfig)
                  00018c2c    00000100     arm_cortexM0l_math.a : arm_common_tables.o (.rodata.twiddleCoef_32)
                  00018d2c    000000bd     lcd.o (.rodata.tfont12)
                  00018de9    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00018dec    00000080     arm_cortexM0l_math.a : arm_common_tables.o (.rodata.twiddleCoef_16)
                  00018e6c    00000070                          : arm_common_tables.o (.rodata.armBitRevIndexTable64)
                  00018edc    00000060                          : arm_common_tables.o (.rodata.armBitRevIndexTable32)
                  00018f3c    00000028                          : arm_common_tables.o (.rodata.armBitRevIndexTable16)
                  00018f64    00000019     empty.o (.rodata.str1.7401042497206923953.1)
                  00018f7d    00000002     empty.o (.rodata.str1.150872071346279890.1)
                  00018f7f    00000001     --HOLE-- [fill = 0]
                  00018f80    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00018f98    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH1Config)
                  00018fb0    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00018fc4    00000010     arm_cortexM0l_math.a : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len1024)
                  00018fd4    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len128)
                  00018fe4    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len16)
                  00018ff4    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len2048)
                  00019004    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len256)
                  00019014    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len32)
                  00019024    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len4096)
                  00019034    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len512)
                  00019044    00000010                          : arm_const_structs.o (.rodata.arm_cfft_sR_f32_len64)
                  00019054    00000010     empty.o (.rodata.str1.9517790425240694019.1)
                  00019064    0000000a     ti_msp_dl_config.o (.rodata.gConsoleConfig)
                  0001906e    0000000a     ti_msp_dl_config.o (.rodata.gTFTspi_config)
                  00019078    00000009     empty.o (.rodata.str1.17669528882079347314.1)
                  00019081    00000009     empty.o (.rodata.str1.9410084661655729462.1)
                  0001908a    00000002     empty.o (.rodata.str1.16704889451495720520.1)
                  0001908c    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00019094    00000008     ti_msp_dl_config.o (.rodata.gADC12_1ClockConfig)
                  0001909c    00000008     empty.o (.rodata.str1.14685083708502177989.1)
                  000190a4    00000008     empty.o (.rodata.str1.254342170260855183.1)
                  000190ac    00000005     empty.o (.rodata.str1.11898133897667081452.1)
                  000190b1    00000004     empty.o (.rodata.str1.15706828512682300538.1)
                  000190b5    00000002     empty.o (.rodata.str1.17686411042290499047.1)
                  000190b7    00000002     empty.o (.rodata.str1.2196762768037919588.1)
                  000190b9    00000007     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00001a5c     UNINITIALIZED
                  20200000    00001800     empty.o (.bss.g_shared_buffer)
                  20201800    000000bc     (.common:gTIMER_0Backup)
                  202018bc    00000080     empty.o (.bss.g_prev_points_ch0)
                  2020193c    00000080     empty.o (.bss.g_prev_points_ch1)
                  202019bc    00000068     empty.o (.bss.g_channels)
                  20201a24    00000028     (.common:gTFTspiBackup)
                  20201a4c    00000010     empty.o (.bss.g_fft_instance)

.data      0    20201a5c    0000082a     UNINITIALIZED
                  20201a5c    00000400     empty.o (.data.gADCSamples)
                  20201e5c    00000400     empty.o (.data.gADCSamples_ch1)
                  2020225c    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20202260    00000004     empty.o (.data.adc0_done)
                  20202264    00000004     empty.o (.data.adc1_done)
                  20202268    00000004     empty.o (.data.g_button_flags)
                  2020226c    00000004     empty.o (.data.g_current_freq_hz)
                  20202270    00000004     empty.o (.data.g_display_update_counter)
                  20202274    00000004     empty.o (.data.g_last_set_freq_hz)
                  20202278    00000004     empty.o (.data.g_system_tick_ms)
                  2020227c    00000004     empty.o (.data.main.last_fft_time)
                  20202280    00000004     empty.o (.data.main.last_freq)
                  20202284    00000001     empty.o (.data.g_system_state)
                  20202285    00000001     empty.o (.data.main.fft_indicator)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       empty.o                        2986    92        8606   
       ti_msp_dl_config.o             2756    111       228    
       adc_channel.o                  1054    0         0      
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         6804    395       8834   
                                                               
    .\BSP\AD9850\
       ad9850.o                       522     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         522     0         0      
                                                               
    .\BSP\LCD\
       lcd.o                          3624    14917     0      
       lcd_init.o                     998     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4622    14917     0      
                                                               
    .\Board\
       board.o                        22      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         22      0         0      
                                                               
    G:/ti/SDK/mspm0-sdk-main/source/third_party/CMSIS/DSP/lib/ticlang/m0p/arm_cortexM0l_math.a
       arm_common_tables.o            0       87128     0      
       arm_cfft_f32.o                 3038    0         0      
       arm_cfft_radix8_f32.o          2252    0         0      
       arm_cmplx_mag_f32.o            360     0         0      
       arm_bitreversal2.o             274     0         0      
       arm_rms_f32.o                  210     0         0      
       arm_offset_f32.o               180     0         0      
       arm_cfft_init_f32.o            176     0         0      
       arm_const_structs.o            0       144       0      
       arm_mean_f32.o                 136     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         6626    87272     0      
                                                               
    G:/ti/SDK/mspm0-sdk-main/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     260     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   100     0         0      
       dl_uart.o                      90      0         0      
       dl_spi.o                       86      0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         686     0         0      
                                                               
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       e_sqrtf.c.obj                  160     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         468     0         4      
                                                               
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   402     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_fcmp.S.obj               98      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatunsisf.S.obj              40      0         0      
       floatunsidf.S.obj              36      0         0      
       aeabi_memset.S.obj             12      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2182    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       50        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   21936   102634    9350   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0001e6b4 records: 2, size/record: 8, table size: 16
	.data: load addr=0001e690, load size=0000000e bytes, run addr=20201a5c, run size=0000082a bytes, compression=lzss
	.bss: load addr=0001e6ac, load size=00000008 bytes, run addr=20200000, run size=00001a5c bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0001e6a0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                              
-------   ----                              
0001e041  ADC0_IRQHandler                   
0001e061  ADC1_IRQHandler                   
0001e677  AES_IRQHandler                    
0001e67a  C$$EXIT                           
0001e677  CANFD0_IRQHandler                 
0001e677  DAC0_IRQHandler                   
0001d941  DL_ADC12_setClockConfig           
0001e645  DL_Common_delayCycles             
0001d749  DL_DMA_initChannel                
0001d875  DL_SPI_init                       
0001e5b9  DL_SPI_setClockConfig             
0001d4c5  DL_SYSCTL_setHFCLKSourceHFXTParams
0001c7e5  DL_Timer_initTimerMode            
0001e231  DL_Timer_setClockConfig           
0001d82d  DL_UART_init                      
0001e5cb  DL_UART_setClockConfig            
0001e677  DMA_IRQHandler                    
0001e677  Default_Handler                   
0001e677  GROUP0_IRQHandler                 
0001d5f1  GROUP1_IRQHandler                 
0001e67b  HOSTexit                          
0001e677  HardFault_Handler                 
0001e677  I2C0_IRQHandler                   
0001e677  I2C1_IRQHandler                   
0001d6a9  LCD_Address_Set                   
0001c079  LCD_DrawLine                      
0001de11  LCD_DrawPoint                     
0001d03d  LCD_Fill                          
0001acc9  LCD_Init                          
0001af29  LCD_ShowChar                      
0001c2bd  LCD_ShowChinese                   
0001b32d  LCD_ShowChinese12x12              
0001b505  LCD_ShowChinese16x16              
0001b6dd  LCD_ShowChinese24x24              
0001b8b5  LCD_ShowChinese32x32              
0001c1a1  LCD_ShowFloatNum1                 
0001d3c9  LCD_ShowString                    
0001e0dd  LCD_WR_DATA                       
0001e531  LCD_WR_DATA8                      
0001dd85  LCD_WR_REG                        
0001d529  LCD_Writ_Bus                      
0001e677  NMI_Handler                       
0001e677  PendSV_Handler                    
0001e677  RTC_IRQHandler                    
0001e67f  Reset_Handler                     
0001e677  SPI0_IRQHandler                   
0001e677  SPI1_IRQHandler                   
0001e677  SVC_Handler                       
0001ce01  SYSCFG_DL_ADC12_0_init            
0001ccc1  SYSCFG_DL_ADC12_1_init            
0001d981  SYSCFG_DL_Console_init            
0001df35  SYSCFG_DL_DMA_CH0_init            
0001de3d  SYSCFG_DL_DMA_CH1_init            
0001e62d  SYSCFG_DL_DMA_init                
0001c6f9  SYSCFG_DL_GPIO_init               
0001e449  SYSCFG_DL_SYSCTL_CLK_init         
0001dc8d  SYSCFG_DL_SYSCTL_init             
0001d9c1  SYSCFG_DL_TFTspi_init             
0001d651  SYSCFG_DL_TIMER_0_init            
0001d8b9  SYSCFG_DL_init                    
0001ce9d  SYSCFG_DL_initPower               
0001e677  SysTick_Handler                   
0001e677  TIMA0_IRQHandler                  
0001e677  TIMA1_IRQHandler                  
0001e677  TIMG0_IRQHandler                  
0001e677  TIMG12_IRQHandler                 
0001e677  TIMG6_IRQHandler                  
0001e677  TIMG7_IRQHandler                  
0001e677  TIMG8_IRQHandler                  
0001e5dd  TI_memcpy_small                   
0001d443  TI_memset_small                   
0001e677  UART0_IRQHandler                  
0001e677  UART1_IRQHandler                  
0001e677  UART2_IRQHandler                  
0001e677  UART3_IRQHandler                  
20208000  __STACK_END                       
00000200  __STACK_SIZE                      
00000000  __TI_ATRegion0_region_sz          
00000000  __TI_ATRegion0_src_addr           
00000000  __TI_ATRegion0_trg_addr           
00000000  __TI_ATRegion1_region_sz          
00000000  __TI_ATRegion1_src_addr           
00000000  __TI_ATRegion1_trg_addr           
00000000  __TI_ATRegion2_region_sz          
00000000  __TI_ATRegion2_src_addr           
00000000  __TI_ATRegion2_trg_addr           
0001e6b4  __TI_CINIT_Base                   
0001e6c4  __TI_CINIT_Limit                  
0001e6c4  __TI_CINIT_Warm                   
0001e6a0  __TI_Handler_Table_Base           
0001e6ac  __TI_Handler_Table_Limit          
0001db35  __TI_auto_init_nobinit_nopinit    
0001d34d  __TI_decompress_lzss              
0001e5ef  __TI_decompress_none              
ffffffff  __TI_pprof_out_hndl               
ffffffff  __TI_prof_data_size               
ffffffff  __TI_prof_data_start              
00000000  __TI_static_base__                
0001e611  __TI_zero_init                    
0001bc57  __adddf3                          
0001c9bb  __addsf3                          
0001d451  __aeabi_d2f                       
0001d8fd  __aeabi_d2uiz                     
0001bc57  __aeabi_dadd                      
0001c4e5  __aeabi_ddiv                      
0001c8cd  __aeabi_dmul                      
0001bc4d  __aeabi_dsub                      
2020225c  __aeabi_errno                     
0001e659  __aeabi_errno_addr                
0001da41  __aeabi_f2d                       
0001dc21  __aeabi_f2iz                      
0001c9bb  __aeabi_fadd                      
0001d58d  __aeabi_fcmpeq                    
0001d5c9  __aeabi_fcmpge                    
0001d5dd  __aeabi_fcmpgt                    
0001d5b5  __aeabi_fcmple                    
0001d5a1  __aeabi_fcmplt                    
0001d1c9  __aeabi_fdiv                      
0001cf29  __aeabi_fmul                      
0001c9b1  __aeabi_fsub                      
0001da81  __aeabi_i2f                       
0001acc7  __aeabi_idiv0                     
0001e639  __aeabi_memclr                    
0001e639  __aeabi_memclr4                   
0001e639  __aeabi_memclr8                   
0001e661  __aeabi_memcpy                    
0001e661  __aeabi_memcpy4                   
0001e661  __aeabi_memcpy8                   
0001e01d  __aeabi_ui2d                      
0001df5d  __aeabi_ui2f                      
0001da01  __aeabi_uidiv                     
0001da01  __aeabi_uidivmod                  
ffffffff  __binit__                         
0001db71  __cmpsf2                          
0001c4e5  __divdf3                          
0001d1c9  __divsf3                          
0001db71  __eqsf2                           
0001da41  __extendsfdf2                     
0001dc21  __fixsfsi                         
0001d8fd  __fixunsdfsi                      
0001da81  __floatsisf                       
0001e01d  __floatunsidf                     
0001df5d  __floatunsisf                     
0001dabd  __gesf2                           
0001dabd  __gtsf2                           
0001db71  __lesf2                           
0001db71  __ltsf2                           
UNDEFED   __mpu_init                        
0001c8cd  __muldf3                          
0001dbad  __muldsi3                         
0001cf29  __mulsf3                          
0001db71  __nesf2                           
20207e00  __stack                           
20200000  __start___llvm_prf_bits           
20200000  __start___llvm_prf_cnts           
20200000  __stop___llvm_prf_bits            
20200000  __stop___llvm_prf_cnts            
0001bc4d  __subdf3                          
0001c9b1  __subsf3                          
0001d451  __truncdfsf2                      
0001df85  _c_int00_noargs                   
UNDEFED   _system_post_cinit                
0001e683  _system_pre_init                  
0001e671  abort                             
20202260  adc0_done                         
20202264  adc1_done                         
0001d2cd  adc_channel_display_values        
0001b13d  adc_channel_draw_waveform         
0001d24d  adc_channel_init                  
0001bf49  adc_channel_process_data          
00015790  armBitRevIndexTable1024           
00018780  armBitRevIndexTable128            
00018f3c  armBitRevIndexTable16             
00010040  armBitRevIndexTable2048           
00017f84  armBitRevIndexTable256            
00018edc  armBitRevIndexTable32             
0000e0c0  armBitRevIndexTable4096           
00017c04  armBitRevIndexTable512            
00018e6c  armBitRevIndexTable64             
0001c3d3  arm_bitreversal_32                
0001ba8d  arm_cfft_f32                      
0001cc11  arm_cfft_init_f32                 
0001aa17  arm_cfft_radix8by2_f32            
0001a2a9  arm_cfft_radix8by4_f32            
00018fc4  arm_cfft_sR_f32_len1024           
00018fd4  arm_cfft_sR_f32_len128            
00018fe4  arm_cfft_sR_f32_len16             
00018ff4  arm_cfft_sR_f32_len2048           
00019004  arm_cfft_sR_f32_len256            
00019014  arm_cfft_sR_f32_len32             
00019024  arm_cfft_sR_f32_len4096           
00019034  arm_cfft_sR_f32_len512            
00019044  arm_cfft_sR_f32_len64             
0001bddf  arm_cmplx_mag_f32                 
0001cfb5  arm_mean_f32                      
0001cb5b  arm_offset_f32                    
000199dd  arm_radix8_butterfly_f32          
0001ca89  arm_rms_f32                       
00017390  ascii_1206                        
00016da0  ascii_1608                        
000135c0  ascii_2412                        
00011e00  ascii_3216                        
ffffffff  binit                             
0001e669  dds_get_ref_clock                 
0001dcc1  dds_reset                         
0001c5f1  dds_set                           
0001e687  dds_verify_config                 
0001e4a3  delay_ms                          
20201a5c  gADCSamples                       
20201e5c  gADCSamples_ch1                   
20201a24  gTFTspiBackup                     
20201800  gTIMER_0Backup                    
20202268  g_button_flags                    
00000000  interruptVectors                  
000190c1  main                              
0001ddb5  mypow                             
0001cd61  sqrtf                             
00018d2c  tfont12                           
00018920  tfont16                           
00018ab8  tfont24                           
000182f4  tfont32                           
0000c0c0  twiddleCoef_1024                  
00017804  twiddleCoef_128                   
00018dec  twiddleCoef_16                    
000080c0  twiddleCoef_2048                  
000165a0  twiddleCoef_256                   
00018c2c  twiddleCoef_32                    
000000c0  twiddleCoef_4096                  
00014790  twiddleCoef_512                   
00018580  twiddleCoef_64                    


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                              
-------   ----                              
00000000  __TI_ATRegion0_region_sz          
00000000  __TI_ATRegion0_src_addr           
00000000  __TI_ATRegion0_trg_addr           
00000000  __TI_ATRegion1_region_sz          
00000000  __TI_ATRegion1_src_addr           
00000000  __TI_ATRegion1_trg_addr           
00000000  __TI_ATRegion2_region_sz          
00000000  __TI_ATRegion2_src_addr           
00000000  __TI_ATRegion2_trg_addr           
00000000  __TI_static_base__                
00000000  interruptVectors                  
000000c0  twiddleCoef_4096                  
00000200  __STACK_SIZE                      
000080c0  twiddleCoef_2048                  
0000c0c0  twiddleCoef_1024                  
0000e0c0  armBitRevIndexTable4096           
00010040  armBitRevIndexTable2048           
00011e00  ascii_3216                        
000135c0  ascii_2412                        
00014790  twiddleCoef_512                   
00015790  armBitRevIndexTable1024           
000165a0  twiddleCoef_256                   
00016da0  ascii_1608                        
00017390  ascii_1206                        
00017804  twiddleCoef_128                   
00017c04  armBitRevIndexTable512            
00017f84  armBitRevIndexTable256            
000182f4  tfont32                           
00018580  twiddleCoef_64                    
00018780  armBitRevIndexTable128            
00018920  tfont16                           
00018ab8  tfont24                           
00018c2c  twiddleCoef_32                    
00018d2c  tfont12                           
00018dec  twiddleCoef_16                    
00018e6c  armBitRevIndexTable64             
00018edc  armBitRevIndexTable32             
00018f3c  armBitRevIndexTable16             
00018fc4  arm_cfft_sR_f32_len1024           
00018fd4  arm_cfft_sR_f32_len128            
00018fe4  arm_cfft_sR_f32_len16             
00018ff4  arm_cfft_sR_f32_len2048           
00019004  arm_cfft_sR_f32_len256            
00019014  arm_cfft_sR_f32_len32             
00019024  arm_cfft_sR_f32_len4096           
00019034  arm_cfft_sR_f32_len512            
00019044  arm_cfft_sR_f32_len64             
000190c1  main                              
000199dd  arm_radix8_butterfly_f32          
0001a2a9  arm_cfft_radix8by4_f32            
0001aa17  arm_cfft_radix8by2_f32            
0001acc7  __aeabi_idiv0                     
0001acc9  LCD_Init                          
0001af29  LCD_ShowChar                      
0001b13d  adc_channel_draw_waveform         
0001b32d  LCD_ShowChinese12x12              
0001b505  LCD_ShowChinese16x16              
0001b6dd  LCD_ShowChinese24x24              
0001b8b5  LCD_ShowChinese32x32              
0001ba8d  arm_cfft_f32                      
0001bc4d  __aeabi_dsub                      
0001bc4d  __subdf3                          
0001bc57  __adddf3                          
0001bc57  __aeabi_dadd                      
0001bddf  arm_cmplx_mag_f32                 
0001bf49  adc_channel_process_data          
0001c079  LCD_DrawLine                      
0001c1a1  LCD_ShowFloatNum1                 
0001c2bd  LCD_ShowChinese                   
0001c3d3  arm_bitreversal_32                
0001c4e5  __aeabi_ddiv                      
0001c4e5  __divdf3                          
0001c5f1  dds_set                           
0001c6f9  SYSCFG_DL_GPIO_init               
0001c7e5  DL_Timer_initTimerMode            
0001c8cd  __aeabi_dmul                      
0001c8cd  __muldf3                          
0001c9b1  __aeabi_fsub                      
0001c9b1  __subsf3                          
0001c9bb  __addsf3                          
0001c9bb  __aeabi_fadd                      
0001ca89  arm_rms_f32                       
0001cb5b  arm_offset_f32                    
0001cc11  arm_cfft_init_f32                 
0001ccc1  SYSCFG_DL_ADC12_1_init            
0001cd61  sqrtf                             
0001ce01  SYSCFG_DL_ADC12_0_init            
0001ce9d  SYSCFG_DL_initPower               
0001cf29  __aeabi_fmul                      
0001cf29  __mulsf3                          
0001cfb5  arm_mean_f32                      
0001d03d  LCD_Fill                          
0001d1c9  __aeabi_fdiv                      
0001d1c9  __divsf3                          
0001d24d  adc_channel_init                  
0001d2cd  adc_channel_display_values        
0001d34d  __TI_decompress_lzss              
0001d3c9  LCD_ShowString                    
0001d443  TI_memset_small                   
0001d451  __aeabi_d2f                       
0001d451  __truncdfsf2                      
0001d4c5  DL_SYSCTL_setHFCLKSourceHFXTParams
0001d529  LCD_Writ_Bus                      
0001d58d  __aeabi_fcmpeq                    
0001d5a1  __aeabi_fcmplt                    
0001d5b5  __aeabi_fcmple                    
0001d5c9  __aeabi_fcmpge                    
0001d5dd  __aeabi_fcmpgt                    
0001d5f1  GROUP1_IRQHandler                 
0001d651  SYSCFG_DL_TIMER_0_init            
0001d6a9  LCD_Address_Set                   
0001d749  DL_DMA_initChannel                
0001d82d  DL_UART_init                      
0001d875  DL_SPI_init                       
0001d8b9  SYSCFG_DL_init                    
0001d8fd  __aeabi_d2uiz                     
0001d8fd  __fixunsdfsi                      
0001d941  DL_ADC12_setClockConfig           
0001d981  SYSCFG_DL_Console_init            
0001d9c1  SYSCFG_DL_TFTspi_init             
0001da01  __aeabi_uidiv                     
0001da01  __aeabi_uidivmod                  
0001da41  __aeabi_f2d                       
0001da41  __extendsfdf2                     
0001da81  __aeabi_i2f                       
0001da81  __floatsisf                       
0001dabd  __gesf2                           
0001dabd  __gtsf2                           
0001db35  __TI_auto_init_nobinit_nopinit    
0001db71  __cmpsf2                          
0001db71  __eqsf2                           
0001db71  __lesf2                           
0001db71  __ltsf2                           
0001db71  __nesf2                           
0001dbad  __muldsi3                         
0001dc21  __aeabi_f2iz                      
0001dc21  __fixsfsi                         
0001dc8d  SYSCFG_DL_SYSCTL_init             
0001dcc1  dds_reset                         
0001dd85  LCD_WR_REG                        
0001ddb5  mypow                             
0001de11  LCD_DrawPoint                     
0001de3d  SYSCFG_DL_DMA_CH1_init            
0001df35  SYSCFG_DL_DMA_CH0_init            
0001df5d  __aeabi_ui2f                      
0001df5d  __floatunsisf                     
0001df85  _c_int00_noargs                   
0001e01d  __aeabi_ui2d                      
0001e01d  __floatunsidf                     
0001e041  ADC0_IRQHandler                   
0001e061  ADC1_IRQHandler                   
0001e0dd  LCD_WR_DATA                       
0001e231  DL_Timer_setClockConfig           
0001e449  SYSCFG_DL_SYSCTL_CLK_init         
0001e4a3  delay_ms                          
0001e531  LCD_WR_DATA8                      
0001e5b9  DL_SPI_setClockConfig             
0001e5cb  DL_UART_setClockConfig            
0001e5dd  TI_memcpy_small                   
0001e5ef  __TI_decompress_none              
0001e611  __TI_zero_init                    
0001e62d  SYSCFG_DL_DMA_init                
0001e639  __aeabi_memclr                    
0001e639  __aeabi_memclr4                   
0001e639  __aeabi_memclr8                   
0001e645  DL_Common_delayCycles             
0001e659  __aeabi_errno_addr                
0001e661  __aeabi_memcpy                    
0001e661  __aeabi_memcpy4                   
0001e661  __aeabi_memcpy8                   
0001e669  dds_get_ref_clock                 
0001e671  abort                             
0001e677  AES_IRQHandler                    
0001e677  CANFD0_IRQHandler                 
0001e677  DAC0_IRQHandler                   
0001e677  DMA_IRQHandler                    
0001e677  Default_Handler                   
0001e677  GROUP0_IRQHandler                 
0001e677  HardFault_Handler                 
0001e677  I2C0_IRQHandler                   
0001e677  I2C1_IRQHandler                   
0001e677  NMI_Handler                       
0001e677  PendSV_Handler                    
0001e677  RTC_IRQHandler                    
0001e677  SPI0_IRQHandler                   
0001e677  SPI1_IRQHandler                   
0001e677  SVC_Handler                       
0001e677  SysTick_Handler                   
0001e677  TIMA0_IRQHandler                  
0001e677  TIMA1_IRQHandler                  
0001e677  TIMG0_IRQHandler                  
0001e677  TIMG12_IRQHandler                 
0001e677  TIMG6_IRQHandler                  
0001e677  TIMG7_IRQHandler                  
0001e677  TIMG8_IRQHandler                  
0001e677  UART0_IRQHandler                  
0001e677  UART1_IRQHandler                  
0001e677  UART2_IRQHandler                  
0001e677  UART3_IRQHandler                  
0001e67a  C$$EXIT                           
0001e67b  HOSTexit                          
0001e67f  Reset_Handler                     
0001e683  _system_pre_init                  
0001e687  dds_verify_config                 
0001e6a0  __TI_Handler_Table_Base           
0001e6ac  __TI_Handler_Table_Limit          
0001e6b4  __TI_CINIT_Base                   
0001e6c4  __TI_CINIT_Limit                  
0001e6c4  __TI_CINIT_Warm                   
20200000  __start___llvm_prf_bits           
20200000  __start___llvm_prf_cnts           
20200000  __stop___llvm_prf_bits            
20200000  __stop___llvm_prf_cnts            
20201800  gTIMER_0Backup                    
20201a24  gTFTspiBackup                     
20201a5c  gADCSamples                       
20201e5c  gADCSamples_ch1                   
2020225c  __aeabi_errno                     
20202260  adc0_done                         
20202264  adc1_done                         
20202268  g_button_flags                    
20207e00  __stack                           
20208000  __STACK_END                       
ffffffff  __TI_pprof_out_hndl               
ffffffff  __TI_prof_data_size               
ffffffff  __TI_prof_data_start              
ffffffff  __binit__                         
ffffffff  binit                             
UNDEFED   __mpu_init                        
UNDEFED   _system_post_cinit                

[230 symbols]
