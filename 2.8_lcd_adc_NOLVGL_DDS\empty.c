#include "ti_msp_dl_config.h"
#include "board.h"
#include "stdio.h"
#include "lcd_init.h"
#include "lcd.h"
#include "pic.h"
#include "wave.h"
#include <math.h>
#include "arm_math.h"
#include "BSP/AD9850/ad9850.h"
#include "adc_channel.h"
// #include "button_control.h"  // 不需要复杂的按键模块

// The GPIO definitions are now self-contained within ad9850.c,
// so they are no longer needed here.

// The callback and delay functions are also no longer needed
// as the new driver is self-contained.

// --- 内存优化：减少ADC缓冲区大小 ---
uint16_t gADCSamples[512] = {0};      // 减少到512个样本，节省1KB内存
int adc0_done = 0;
uint16_t gADCSamples_ch1[512] = {0};  // 减少到512个样本，节省1KB内存
int adc1_done = 0;

// --- 内存优化：减少内存占用，使用共享缓冲区 ---
// 使用联合体共享内存空间，因为浮点处理和FFT不会同时进行
typedef union {
    struct {
        float32_t current[512];   // 减少到512点，降低内存使用
        float32_t voltage[512];   // 减少到512点
    } channels;
    struct {
        float32_t input[1024];    // FFT输入缓冲区，复用通道数据空间
        float32_t output[512];    // FFT输出缓冲区，减少到512点
    } fft;
} shared_buffer_t;

static shared_buffer_t g_shared_buffer;  // 共享缓冲区，总大小约4KB
static arm_cfft_instance_f32 g_fft_instance;


// --- 代码重构：使用通道管理结构替代重复代码 ---
static Point g_prev_points_ch0[32];
static Point g_prev_points_ch1[32];
static adc_channel_t g_channels[2];  // 两个ADC通道

// --- 新增：用于动态控制AD9850频率 ---
static uint32_t g_current_freq_hz = 4555; // 初始频率 10kHz
static uint32_t g_last_set_freq_hz = 0;    // 上次设置的频率，避免重复设置

// --- 按键中断标志 ---
volatile uint32_t g_button_flags = 0;
#define BUTTON_UP_FLAG   0x01
#define BUTTON_DOWN_FLAG 0x02

// --- 性能优化：系统状态管理 ---
typedef enum {
    SYS_STATE_IDLE = 0,
    SYS_STATE_DATA_READY,
    SYS_STATE_PROCESSING
} system_state_t;

static volatile system_state_t g_system_state = SYS_STATE_IDLE;

// --- 性能优化：减少不必要的显示更新 ---
static uint32_t g_display_update_counter = 0;
#define DISPLAY_UPDATE_INTERVAL 5  // 每5次循环更新一次显示

// --- FFT时间管理优化 ---
static volatile uint32_t g_system_tick_ms = 0;  // 系统时间计数器（毫秒）
#define FFT_UPDATE_INTERVAL_MS 1000  // FFT每1秒更新一次

// --- 频率校准参数（可根据实际测试调整） ---
#define SAMPLE_RATE_CALIBRATION 39900.0f  // 校准后的采样率，解决0.1kHz偏差
// 如果频率仍然偏高，可以尝试：39800.0f, 39850.0f 等值
// 如果频率偏低，可以尝试：39950.0f, 40000.0f 等值

// --- 内存优化：预分配临时缓冲区 ---
static Point g_temp_points[32];  // 避免在函数中栈分配




int main(void)
{
    SYSCFG_DL_init();

    LCD_Init(); // LCD初始化

    // 初始化DDS模块
    dds_reset();

    // 验证DDS配置
    if (dds_verify_config()) {
        // 显示DDS参考时钟信息（调试用）
        uint32_t ref_clock = dds_get_ref_clock();
        // 可以通过串口输出或LCD显示，这里暂时注释
        // printf("DDS Reference Clock: %lu Hz\n", ref_clock);
    }

    // 初始化CMSIS-DSP FFT实例（512点FFT，内存优化）
    arm_status fft_status = arm_cfft_init_f32(&g_fft_instance, 512);
    if (fft_status != ARM_MATH_SUCCESS) {
        // FFT初始化失败，可以在LCD上显示错误
        LCD_ShowString(10, 65, (const u8 *)"FFT Init Failed", RED, GREEN, 16, 0);
    }

    // 优化：初始设置DDS频率并记录
    dds_set(g_current_freq_hz);
    g_last_set_freq_hz = g_current_freq_hz;

    // 确保DDS持续输出
    delay_ms(100);  // 给DDS一些时间稳定

    // --- 优化：预配置DMA，减少传输大小以节省内存 ---
    // DMA CH0 配置 - 使用512个样本减少内存使用
    DL_DMA_setTransferSize(DMA, DMA_CH0_CHAN_ID, 512);
  	DL_DMA_setSrcAddr(DMA, DMA_CH0_CHAN_ID, DL_ADC12_getMemResultAddress(ADC12_0_INST, ADC12_0_ADCMEM_0));
  	DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)&gADCSamples[0]);
  	DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);

    // DMA CH1 配置 - 使用512个样本减少内存使用
    DL_DMA_setTransferSize(DMA, DMA_CH1_CHAN_ID, 512);
  	DL_DMA_setSrcAddr(DMA, DMA_CH1_CHAN_ID, DL_ADC12_getMemResultAddress(ADC12_1_INST, ADC12_1_ADCMEM_0));
  	DL_DMA_setDestAddr(DMA, DMA_CH1_CHAN_ID, (uint32_t)&gADCSamples_ch1[0]);
  	DL_DMA_enableChannel(DMA, DMA_CH1_CHAN_ID);
    
    LCD_Fill(0,0,LCD_W,LCD_H,GREEN);
    delay_ms(100);
    
    LCD_ShowString(10, 5, (const u8 *)"Voltage", BLACK, GREEN, 16, 0);
    LCD_ShowString(10, 25, (const u8 *)"Current", BLACK, GREEN, 16, 0);
    LCD_ShowString(10, 45, (const u8 *)"DDS Freq", BLACK, GREEN, 16, 0);
    // 只显示FFT频率标签
    LCD_ShowString(160, 5, (const u8 *)"FFT:", RED, GREEN, 16, 0);
	LCD_ShowChinese(100,220,(u8 *)"关注吸勾勾谢谢喵",YELLOW,GREEN,16,1);
      
    // Conversion factors based on 3.3V VREF and assumed hardware
    const float VREF = 3.3f;
    const int ADC_MAX = 4095;

    // Voltage conversion: Direct connection, ratio is 1:1
    const float VOLTAGE_CONVERSION_FACTOR = (VREF / ADC_MAX) * 1.0f;

    // Current conversion: Direct connection, ratio is 1:1 (measures voltage, not amps)
    const float CURRENT_CONVERSION_FACTOR = (VREF / ADC_MAX) * 1.0f;

    // --- 初始化ADC通道管理结构（使用共享缓冲区） ---
    adc_channel_init(&g_channels[0], gADCSamples, g_shared_buffer.channels.current, g_prev_points_ch0,
                     &adc0_done, BLUE, "Current", "A", CURRENT_CONVERSION_FACTOR);
    adc_channel_init(&g_channels[1], gADCSamples_ch1, g_shared_buffer.channels.voltage, g_prev_points_ch1,
                     &adc1_done, RED, "Voltage", "V", VOLTAGE_CONVERSION_FACTOR);

    // 初始显示频率
    LCD_ShowFloatNum1(85, 45, (float)g_current_freq_hz / 1000.0f, 4, BLACK, GREEN, 16);
    LCD_ShowString(135, 45, (const u8 *)"kHz", BLACK, GREEN, 16, 0);

	NVIC_EnableIRQ(ADC12_0_INST_INT_IRQN);
    // --- 新增：启用ADC1的中断 ---
	NVIC_EnableIRQ(ADC12_1_INST_INT_IRQN);
    // --- 优化中断优先级设置 ---
    NVIC_SetPriority(ADC12_0_INST_INT_IRQN, 0);  // ADC中断最高优先级
    NVIC_SetPriority(ADC12_1_INST_INT_IRQN, 0);  // ADC中断最高优先级
    NVIC_SetPriority(BUTTONS_INT_IRQN, 2);       // 按键中断较低优先级
    NVIC_EnableIRQ(BUTTONS_INT_IRQN);

  	DL_Timer_startCounter(TIMER_0_INST);

 	while(1)
 	{
        // --- 性能优化：状态驱动的数据处理 ---
        bool data_updated = false;

		if (adc0_done == 1) {
            // 优化：只重新启用DMA，避免重复配置
            DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);
            adc_channel_process_data(&g_channels[0], 512);  // 使用512点处理，减少内存使用
            adc0_done = 0;
            data_updated = true;
            g_system_state = SYS_STATE_DATA_READY;
        }

        // --- 处理第二路ADC的数据 ---
		if (adc1_done == 1) {
            // 优化：只重新启用DMA，避免重复配置
            DL_DMA_enableChannel(DMA, DMA_CH1_CHAN_ID);
            adc_channel_process_data(&g_channels[1], 512);  // 使用512点处理，减少内存使用
            adc1_done = 0;
            data_updated = true;
            g_system_state = SYS_STATE_DATA_READY;
        }

        // --- 优化：只在数据更新时才刷新显示 ---
        if (data_updated) {
            g_display_update_counter++;
            // 降低显示更新频率，减少LCD操作开销
            if (g_display_update_counter >= DISPLAY_UPDATE_INTERVAL) {
                adc_channel_display_values(&g_channels[1], 85, 5, BLACK, GREEN);   // 电压
                adc_channel_display_values(&g_channels[0], 85, 25, BLACK, GREEN);  // 电流
                g_display_update_counter = 0;
            }
        }
		
        // --- 优化：按键处理和DDS频率更新 ---
        if (g_button_flags & BUTTON_UP_FLAG) {
            g_button_flags &= ~BUTTON_UP_FLAG;  // 清除标志
            if (g_current_freq_hz < 20000) {  // 调整为20kHz上限，匹配检测范围
                g_current_freq_hz += 1000;
                // 立即更新显示
                LCD_ShowFloatNum1(85, 45, (float)g_current_freq_hz / 1000.0f, 4, BLACK, GREEN, 16);
                LCD_ShowString(135, 45, (const u8 *)"kHz", BLACK, GREEN, 16, 0);
            }
        }

        if (g_button_flags & BUTTON_DOWN_FLAG) {
            g_button_flags &= ~BUTTON_DOWN_FLAG;  // 清除标志
            if (g_current_freq_hz > 1000) {
                g_current_freq_hz -= 1000;
                // 立即更新显示
                LCD_ShowFloatNum1(85, 45, (float)g_current_freq_hz / 1000.0f, 4, BLACK, GREEN, 16);
                LCD_ShowString(135, 45, (const u8 *)"kHz", BLACK, GREEN, 16, 0);
            }
        }

        // --- 优化：只在频率改变时设置DDS ---
        if (g_current_freq_hz != g_last_set_freq_hz) {
            dds_set(g_current_freq_hz);
            g_last_set_freq_hz = g_current_freq_hz;
        }

        // --- 修复：FFT频率计算（基于精确时间的1秒刷新） ---
        static uint32_t last_fft_time = 0;

        // 更新系统时间（基于主循环延时估算）
        g_system_tick_ms += 100;  // 假设主循环约100ms，可根据实际调整

        // 每1000ms（1秒）计算一次FFT，且需要有新数据
        if (data_updated && (g_system_tick_ms - last_fft_time >= FFT_UPDATE_INTERVAL_MS)) {
            static float last_freq = 0.0f;

            // 检查ADC数据是否有效（修复：使用512点数据）
            uint16_t min_val = 4095, max_val = 0;
            for (int i = 0; i < 512; i++) {
                if (gADCSamples[i] < min_val) min_val = gADCSamples[i];
                if (gADCSamples[i] > max_val) max_val = gADCSamples[i];
            }

            bool has_valid_signal = (max_val - min_val) > 50;  // 至少50个ADC码的变化

            // // 强制显示调试信息
            // LCD_ShowIntNum(10, 65, min_val, 4, BLUE, GREEN, 16);
            // LCD_ShowString(50, 65, (const u8 *)"min", BLUE, GREEN, 16, 0);
            // LCD_ShowIntNum(80, 65, max_val, 4, BLUE, GREEN, 16);
            // LCD_ShowString(120, 65, (const u8 *)"max", BLUE, GREEN, 16, 0);

            if (has_valid_signal) {
                // 优化的FFT数据准备（512点，内存优化版本）
                // 计算DC偏移，使用前512个样本
                float dc_offset = 0.0f;
                for (int i = 0; i < 512; i++) {
                    dc_offset += (float)gADCSamples[i];
                }
                dc_offset /= 512.0f;

                // 填充FFT输入数组，去除DC分量，使用共享缓冲区
                for (int i = 0; i < 512; i++) {
                    g_shared_buffer.fft.input[2*i] = (float)gADCSamples[i] - dc_offset;  // 实部
                    g_shared_buffer.fft.input[2*i + 1] = 0.0f;  // 虚部设为0
                }

                // 执行512点FFT
                arm_cfft_f32(&g_fft_instance, g_shared_buffer.fft.input, 0, 1);  // 0=FFT, 1=bit reversal

                // 计算幅度谱
                arm_cmplx_mag_f32(g_shared_buffer.fft.input, g_shared_buffer.fft.output, 512);

                // 寻找最大幅度对应的频率（忽略DC分量，从索引1开始）
                uint32_t max_index = 1;
                float max_magnitude = g_shared_buffer.fft.output[1];

                // 修复：精确的FFT分析（使用校准后的采样率）
                float freq_resolution = SAMPLE_RATE_CALIBRATION / 512.0f;  // 使用校准参数

                // 计算搜索范围：1kHz到20kHz对应的bin索引
                int start_bin = (int)(1000.0f / freq_resolution);   // ~13
                int end_bin = (int)(20000.0f / freq_resolution);    // ~256 (奈奎斯特频率)
                if (end_bin > 256) end_bin = 256;

                // 寻找峰值频率
                for (int i = start_bin; i < end_bin; i++) {
                    if (g_shared_buffer.fft.output[i] > max_magnitude) {
                        max_magnitude = g_shared_buffer.fft.output[i];
                        max_index = i;
                    }
                }

                // 修复：更精确的频率计算，使用插值提高精度
                float detected_freq = max_index * freq_resolution;

                // 可选：使用抛物线插值进一步提高频率精度
                if (max_index > 0 && max_index < 255) {
                    float y1 = g_shared_buffer.fft.output[max_index - 1];
                    float y2 = g_shared_buffer.fft.output[max_index];
                    float y3 = g_shared_buffer.fft.output[max_index + 1];

                    // 抛物线插值计算精确峰值位置
                    float delta = 0.5f * (y3 - y1) / (2.0f * y2 - y1 - y3);
                    detected_freq = (max_index + delta) * freq_resolution;
                }

                // 频率范围滤波和平滑处理
                if (detected_freq >= 800 && detected_freq <= 21000 && max_magnitude > 50) {
                    // 减少滤波系数，提高响应速度
                    last_freq = last_freq * 0.3f + detected_freq * 0.7f;
                }

                // 更新FFT计算时间
                last_fft_time = g_system_tick_ms;

                // 显示FFT频率（每秒更新一次）
                LCD_ShowFloatNum1(200, 5, last_freq / 1000.0f, 4, RED, GREEN, 16);
                LCD_ShowString(250, 5, (const u8 *)"kHz", RED, GREEN, 16, 0);

                // 可选：显示FFT更新指示器（闪烁点表示FFT正在工作）
                static bool fft_indicator = false;
                fft_indicator = !fft_indicator;
                if (fft_indicator) {
                    LCD_ShowString(280, 5, (const u8 *)"*", RED, GREEN, 16, 0);
                } else {
                    LCD_ShowString(280, 5, (const u8 *)" ", RED, GREEN, 16, 0);
                }

                // // 调试信息：显示最大bin索引和幅度（可取消注释用于调试）
                // LCD_ShowIntNum(200, 25, max_index, 3, RED, GREEN, 16);
                // LCD_ShowString(230, 25, (const u8 *)"bin", RED, GREEN, 16, 0);
                // LCD_ShowIntNum(200, 45, (uint16_t)max_magnitude, 4, RED, GREEN, 16);
                // LCD_ShowString(240, 45, (const u8 *)"mag", RED, GREEN, 16, 0);
            } else {
                // 无有效信号时显示0
                LCD_ShowString(200, 5, (const u8 *)"0.000kHz", RED, GREEN, 16, 0);
            }
        }

		// --- 优化：波形绘制（分离显示区域，避免重叠） ---
		if (data_updated) {
            u16 x0 = 0;
            u16 width = 320;
            u16 data_len = 32;
            u16 bgcolor = GREEN;

            // 为两路信号分配不同的显示区域，避免重叠
            u16 current_y0 = 80;   // 电流波形区域
            u16 voltage_y0 = 140;  // 电压波形区域
            u16 wave_height = 60;  // 每个波形区域高度

            // 分别绘制两路波形
            adc_channel_draw_waveform(&g_channels[0], x0, current_y0, width, wave_height, data_len, bgcolor);  // 电流
            adc_channel_draw_waveform(&g_channels[1], x0, voltage_y0, width, wave_height, data_len, bgcolor);  // 电压

            g_system_state = SYS_STATE_IDLE;  // 处理完成，回到空闲状态
        }

        // --- 优化：动态延时，在空闲时进入低功耗模式 ---
        if (g_system_state == SYS_STATE_IDLE) {
            delay_ms(50);  // 减少延时，提高响应性
            // 可选：在支持的平台上使用 __WFI() 进入低功耗模式
        } else {
            delay_ms(10);  // 有数据处理时使用更短的延时
        }
	}
}


void ADC12_0_INST_IRQHandler() {
  if (DL_ADC12_getPendingInterrupt(ADC12_0_INST) == DL_ADC12_IIDX_DMA_DONE) {
    adc0_done = 1;
  }
}


// --- 新增：ADC1的中断服务函数 ---
void ADC12_1_INST_IRQHandler() {
  if (DL_ADC12_getPendingInterrupt(ADC12_1_INST) == DL_ADC12_IIDX_DMA_DONE) {
    adc1_done = 1;
  }
}

// --- 正确的MSPM0中断处理（使用GROUP1） ---
void GROUP1_IRQHandler(void) {
    // 检查是否是GPIOB中断
    uint32_t group_iidx = DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1);

    if (group_iidx == BUTTONS_INT_IIDX) {
        uint32_t gpio_iidx = DL_GPIO_getPendingInterrupt(BUTTONS_PORT);

        // 立即清除中断标志
        DL_GPIO_clearInterruptStatus(BUTTONS_PORT, gpio_iidx);

        // 只设置标志，不在中断中处理复杂逻辑
        if (gpio_iidx == BUTTONS_FREQ_UP_IIDX) {
            g_button_flags |= BUTTON_UP_FLAG;
        }
        if (gpio_iidx == BUTTONS_FREQ_DOWN_IIDX) {
            g_button_flags |= BUTTON_DOWN_FLAG;
        }
    }
}