<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker Unix v4.0.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/bin/tiarmlnk -I/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib -o 0.96lcd copy.out -m0.96lcd copy.map -i/home/<USER>/ti/mspm0_sdk_2_04_00_06/source -i/home/<USER>/workspace_ccstheia/0.96lcd copy -i/home/<USER>/workspace_ccstheia/0.96lcd copy/Debug/syscfg -i/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=0.96lcd copy_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./BSP/LCD/lcd.o ./BSP/LCD/lcd_init.o ./Board/board.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x687f6094</link_time>
   <link_errors>0x0</link_errors>
   <output_file>/home/<USER>/workspace_ccstheia/0.96lcd copy/Debug/0.96lcd copy.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x6ac5</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd copy/Debug/./</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd copy/Debug/./</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd copy/Debug/./</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd copy/Debug/./BSP/LCD/</path>
         <kind>object</kind>
         <file>lcd.o</file>
         <name>lcd.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd copy/Debug/./BSP/LCD/</path>
         <kind>object</kind>
         <file>lcd_init.o</file>
         <name>lcd_init.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd copy/Debug/./Board/</path>
         <kind>object</kind>
         <file>board.o</file>
         <name>board.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>/home/<USER>/workspace_ccstheia/0.96lcd copy/Debug/</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-14">
         <path>/home/<USER>/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-15">
         <path>/home/<USER>/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_spi.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>/home/<USER>/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>/home/<USER>/ti/mspm0_sdk_2_04_00_06/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_sysctl_mspm0g1x0x_g3x0x.o</name>
      </input_file>
      <input_file id="fl-2e">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>vsnprintf.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-3b">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-3c">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fputs.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_io_perm.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>setvbuf.c.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fopen.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fseek.c.obj</name>
      </input_file>
      <input_file id="fl-4d">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fclose.c.obj</name>
      </input_file>
      <input_file id="fl-ed">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-ee">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-ef">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-f0">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>remove.c.obj</name>
      </input_file>
      <input_file id="fl-f1">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>open.c.obj</name>
      </input_file>
      <input_file id="fl-f2">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>lseek.c.obj</name>
      </input_file>
      <input_file id="fl-f3">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>close.c.obj</name>
      </input_file>
      <input_file id="fl-f4">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>unlink.c.obj</name>
      </input_file>
      <input_file id="fl-f5">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-f6">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-f7">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-f8">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-f9">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-fa">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-fb">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-fc">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-fd">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-fe">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-ff">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-100">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-101">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-102">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>extendsfdf2.S.obj</name>
      </input_file>
      <input_file id="fl-103">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-104">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-105">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-106">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-107">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>truncdfsf2.S.obj</name>
      </input_file>
      <input_file id="fl-108">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-109">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-10a">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-10b">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-10c">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-10d">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-10e">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-10f">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-110">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-111">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-112">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>getdevice.c.obj</name>
      </input_file>
      <input_file id="fl-113">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-114">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
      <input_file id="fl-115">
         <path>/home/<USER>/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib/armv6m-ti-none-eabi/c/</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strcmp-armv6m.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-84">
         <name>.text.LCD_Init</name>
         <load_address>0x3a50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3a50</run_address>
         <size>0xfaa</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x49fa</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x49fa</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text.LCD_ShowChar</name>
         <load_address>0x49fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x49fc</run_address>
         <size>0x400</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.text.LCD_Fill</name>
         <load_address>0x4dfc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4dfc</run_address>
         <size>0x2c0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.text.LCD_ShowChinese12x12</name>
         <load_address>0x50bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x50bc</run_address>
         <size>0x27c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.text.LCD_ShowChinese16x16</name>
         <load_address>0x5338</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5338</run_address>
         <size>0x27c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.LCD_ShowChinese24x24</name>
         <load_address>0x55b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x55b4</run_address>
         <size>0x27c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.LCD_ShowChinese32x32</name>
         <load_address>0x5830</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5830</run_address>
         <size>0x27c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text.main</name>
         <load_address>0x5aac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5aac</run_address>
         <size>0x278</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-df">
         <name>.text.LCD_Address_Set</name>
         <load_address>0x5d24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5d24</run_address>
         <size>0x22c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.text.adddf3_subdf3</name>
         <load_address>0x5f50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5f50</run_address>
         <size>0x192</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x60e2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x60e2</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-99">
         <name>.text.LCD_ShowFloatNum1</name>
         <load_address>0x60e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x60e4</run_address>
         <size>0x134</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-98">
         <name>.text.LCD_ShowIntNum</name>
         <load_address>0x6218</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6218</run_address>
         <size>0x10a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-110">
         <name>.text.DL_SYSCTL_configSYSPLL</name>
         <load_address>0x6324</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6324</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-96">
         <name>.text.LCD_ShowChinese</name>
         <load_address>0x6400</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6400</run_address>
         <size>0xb2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text.__mulsf3</name>
         <load_address>0x64b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x64b4</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-100"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.text.__truncdfsf2</name>
         <load_address>0x6540</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6540</run_address>
         <size>0x74</size>
         <alignment>0x10</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.text.LCD_WR_DATA</name>
         <load_address>0x65b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x65b4</run_address>
         <size>0x70</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.text.DL_SYSCTL_setHFCLKSourceHFXTParams</name>
         <load_address>0x6624</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6624</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x6688</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6688</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x66ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x66ec</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x674c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x674c</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.text.SYSCFG_DL_SPI_LCD_init</name>
         <load_address>0x679c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x679c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-97">
         <name>.text.LCD_ShowString</name>
         <load_address>0x67e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x67e8</run_address>
         <size>0x4a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-119">
         <name>.text.DL_UART_init</name>
         <load_address>0x6834</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6834</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-122">
         <name>.text.DL_SPI_init</name>
         <load_address>0x687c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x687c</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-111">
         <name>.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <load_address>0x68c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x68c0</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x6904</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6904</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0x6944</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6944</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.text.__extendsfdf2</name>
         <load_address>0x6984</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6984</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x69c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x69c4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-133">
         <name>.text.__muldsi3</name>
         <load_address>0x6a00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a00</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.text.__fixsfsi</name>
         <load_address>0x6a3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a3c</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text.SYSCFG_DL_SYSCTL_CLK_init</name>
         <load_address>0x6a74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a74</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x6a9c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a9c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x6ac4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6ac4</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-46">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0x6aec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6aec</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.text.DL_SPI_setClockConfig</name>
         <load_address>0x6b02</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b02</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-113">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x6b14</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b14</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-90">
         <name>.text.delay_ms</name>
         <load_address>0x6b28</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b28</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-80">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x6b38</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b38</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.text:abort</name>
         <load_address>0x6b42</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b42</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.text.HOSTexit</name>
         <load_address>0x6b48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b48</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-ed"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x6b4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b4c</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.text._system_pre_init</name>
         <load_address>0x6b50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b50</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.cinit..bss.load</name>
         <load_address>0x6b60</load_address>
         <readonly>true</readonly>
         <run_address>0x6b60</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-18d">
         <name>__TI_handler_table</name>
         <load_address>0x6b68</load_address>
         <readonly>true</readonly>
         <run_address>0x6b68</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-18e">
         <name>__TI_cinit_table</name>
         <load_address>0x6b6c</load_address>
         <readonly>true</readonly>
         <run_address>0x6b6c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-12c">
         <name>.rodata.ascii_3216</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <run_address>0xc0</run_address>
         <size>0x17c0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-129">
         <name>.rodata.ascii_2412</name>
         <load_address>0x1880</load_address>
         <readonly>true</readonly>
         <run_address>0x1880</run_address>
         <size>0x11d0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.rodata.ascii_1608</name>
         <load_address>0x2a50</load_address>
         <readonly>true</readonly>
         <run_address>0x2a50</run_address>
         <size>0x5f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.rodata.ascii_1206</name>
         <load_address>0x3040</load_address>
         <readonly>true</readonly>
         <run_address>0x3040</run_address>
         <size>0x474</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-128">
         <name>.rodata.tfont32</name>
         <load_address>0x34b4</load_address>
         <readonly>true</readonly>
         <run_address>0x34b4</run_address>
         <size>0x28a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-127">
         <name>.rodata.tfont24</name>
         <load_address>0x373e</load_address>
         <readonly>true</readonly>
         <run_address>0x373e</run_address>
         <size>0x172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-125">
         <name>.rodata.tfont16</name>
         <load_address>0x38b0</load_address>
         <readonly>true</readonly>
         <run_address>0x38b0</run_address>
         <size>0xaa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-126">
         <name>.rodata.tfont12</name>
         <load_address>0x395a</load_address>
         <readonly>true</readonly>
         <run_address>0x395a</run_address>
         <size>0x87</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-123">
         <name>.rodata.gSPI_LCD_clockConfig</name>
         <load_address>0x39e1</load_address>
         <readonly>true</readonly>
         <run_address>0x39e1</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-112">
         <name>.rodata.gSYSPLLConfig</name>
         <load_address>0x39e4</load_address>
         <readonly>true</readonly>
         <run_address>0x39e4</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.rodata.str1.9517790425240694019.1</name>
         <load_address>0x3a0c</load_address>
         <readonly>true</readonly>
         <run_address>0x3a0c</run_address>
         <size>0x10</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-124">
         <name>.rodata.gSPI_LCD_config</name>
         <load_address>0x3a1c</load_address>
         <readonly>true</readonly>
         <run_address>0x3a1c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x3a26</load_address>
         <readonly>true</readonly>
         <run_address>0x3a26</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.rodata.str1.14685083708502177989.1</name>
         <load_address>0x3a30</load_address>
         <readonly>true</readonly>
         <run_address>0x3a30</run_address>
         <size>0x7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.rodata.str1.254342170260855183.1</name>
         <load_address>0x3a37</load_address>
         <readonly>true</readonly>
         <run_address>0x3a37</run_address>
         <size>0x7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x3a3e</load_address>
         <readonly>true</readonly>
         <run_address>0x3a3e</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.rodata.str1.11898133897667081452.1</name>
         <load_address>0x3a40</load_address>
         <readonly>true</readonly>
         <run_address>0x3a40</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.rodata.str1.16704889451495720520.1</name>
         <load_address>0x3a42</load_address>
         <readonly>true</readonly>
         <run_address>0x3a42</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.rodata.str1.17669528882079347314.1</name>
         <load_address>0x3a44</load_address>
         <readonly>true</readonly>
         <run_address>0x3a44</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.rodata.str1.7401042497206923953.1</name>
         <load_address>0x3a46</load_address>
         <readonly>true</readonly>
         <run_address>0x3a46</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-157">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-d7">
         <name>.common:gSPI_LCDBackup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-191">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_loc</name>
         <load_address>0x1f</load_address>
         <run_address>0x1f</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_loc</name>
         <load_address>0x184</load_address>
         <run_address>0x184</run_address>
         <size>0x2bb2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_loc</name>
         <load_address>0x2d36</load_address>
         <run_address>0x2d36</run_address>
         <size>0x1150</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_loc</name>
         <load_address>0x3e86</load_address>
         <run_address>0x3e86</run_address>
         <size>0x260</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_loc</name>
         <load_address>0x40e6</load_address>
         <run_address>0x40e6</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_loc</name>
         <load_address>0x40f9</load_address>
         <run_address>0x40f9</run_address>
         <size>0x816</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-114">
         <name>.debug_loc</name>
         <load_address>0x490f</load_address>
         <run_address>0x490f</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_loc</name>
         <load_address>0x50cb</load_address>
         <run_address>0x50cb</run_address>
         <size>0x414</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_loc</name>
         <load_address>0x54df</load_address>
         <run_address>0x54df</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_loc</name>
         <load_address>0x55b7</load_address>
         <run_address>0x55b7</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_loc</name>
         <load_address>0x59db</load_address>
         <run_address>0x59db</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_loc</name>
         <load_address>0x5b47</load_address>
         <run_address>0x5b47</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.debug_loc</name>
         <load_address>0x5b6d</load_address>
         <run_address>0x5b6d</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_abbrev</name>
         <load_address>0xb3</load_address>
         <run_address>0xb3</run_address>
         <size>0x221</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_abbrev</name>
         <load_address>0x2d4</load_address>
         <run_address>0x2d4</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_abbrev</name>
         <load_address>0x341</load_address>
         <run_address>0x341</run_address>
         <size>0x213</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_abbrev</name>
         <load_address>0x554</load_address>
         <run_address>0x554</run_address>
         <size>0x1cf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_abbrev</name>
         <load_address>0x723</load_address>
         <run_address>0x723</run_address>
         <size>0x232</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_abbrev</name>
         <load_address>0x955</load_address>
         <run_address>0x955</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-148">
         <name>.debug_abbrev</name>
         <load_address>0x9b7</load_address>
         <run_address>0x9b7</run_address>
         <size>0x277</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-146">
         <name>.debug_abbrev</name>
         <load_address>0xc2e</load_address>
         <run_address>0xc2e</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-144">
         <name>.debug_abbrev</name>
         <load_address>0xec9</load_address>
         <run_address>0xec9</run_address>
         <size>0x218</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_abbrev</name>
         <load_address>0x10e1</load_address>
         <run_address>0x10e1</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_abbrev</name>
         <load_address>0x1190</load_address>
         <run_address>0x1190</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_abbrev</name>
         <load_address>0x1300</load_address>
         <run_address>0x1300</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_abbrev</name>
         <load_address>0x1339</load_address>
         <run_address>0x1339</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_abbrev</name>
         <load_address>0x13fb</load_address>
         <run_address>0x13fb</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_abbrev</name>
         <load_address>0x1493</load_address>
         <run_address>0x1493</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ed"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.debug_abbrev</name>
         <load_address>0x14bf</load_address>
         <run_address>0x14bf</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.debug_abbrev</name>
         <load_address>0x14e6</load_address>
         <run_address>0x14e6</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_abbrev</name>
         <load_address>0x150d</load_address>
         <run_address>0x150d</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-100"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.debug_abbrev</name>
         <load_address>0x1534</load_address>
         <run_address>0x1534</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_abbrev</name>
         <load_address>0x155b</load_address>
         <run_address>0x155b</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.debug_abbrev</name>
         <load_address>0x1582</load_address>
         <run_address>0x1582</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_abbrev</name>
         <load_address>0x15a9</load_address>
         <run_address>0x15a9</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.debug_abbrev</name>
         <load_address>0x15d0</load_address>
         <run_address>0x15d0</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-193">
         <name>.debug_abbrev</name>
         <load_address>0x1629</load_address>
         <run_address>0x1629</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2cf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_info</name>
         <load_address>0x2cf</load_address>
         <run_address>0x2cf</run_address>
         <size>0x289d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x2b6c</load_address>
         <run_address>0x2b6c</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_info</name>
         <load_address>0x2bec</load_address>
         <run_address>0x2bec</run_address>
         <size>0x178b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_info</name>
         <load_address>0x4377</load_address>
         <run_address>0x4377</run_address>
         <size>0x6e85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_info</name>
         <load_address>0xb1fc</load_address>
         <run_address>0xb1fc</run_address>
         <size>0xaa5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_info</name>
         <load_address>0xbca1</load_address>
         <run_address>0xbca1</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.debug_info</name>
         <load_address>0xbd16</load_address>
         <run_address>0xbd16</run_address>
         <size>0x1142</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-115">
         <name>.debug_info</name>
         <load_address>0xce58</load_address>
         <run_address>0xce58</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_info</name>
         <load_address>0xe0fe</load_address>
         <run_address>0xe0fe</run_address>
         <size>0x1090</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xf18e</load_address>
         <run_address>0xf18e</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_info</name>
         <load_address>0xf5b1</load_address>
         <run_address>0xf5b1</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_info</name>
         <load_address>0xfcf5</load_address>
         <run_address>0xfcf5</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_info</name>
         <load_address>0xfd3b</load_address>
         <run_address>0xfd3b</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_info</name>
         <load_address>0xfecd</load_address>
         <run_address>0xfecd</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_info</name>
         <load_address>0xffc5</load_address>
         <run_address>0xffc5</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ed"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_info</name>
         <load_address>0x10000</load_address>
         <run_address>0x10000</run_address>
         <size>0x1b7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-134">
         <name>.debug_info</name>
         <load_address>0x101b7</load_address>
         <run_address>0x101b7</run_address>
         <size>0x19f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.debug_info</name>
         <load_address>0x10356</load_address>
         <run_address>0x10356</run_address>
         <size>0x19d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-100"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_info</name>
         <load_address>0x104f3</load_address>
         <run_address>0x104f3</run_address>
         <size>0x1a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_info</name>
         <load_address>0x1069a</load_address>
         <run_address>0x1069a</run_address>
         <size>0x19f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_info</name>
         <load_address>0x10839</load_address>
         <run_address>0x10839</run_address>
         <size>0x1a5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_info</name>
         <load_address>0x109de</load_address>
         <run_address>0x109de</run_address>
         <size>0x1cc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_info</name>
         <load_address>0x10baa</load_address>
         <run_address>0x10baa</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-192">
         <name>.debug_info</name>
         <load_address>0x10c2f</load_address>
         <run_address>0x10c2f</run_address>
         <size>0xa7</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x188</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_str</name>
         <load_address>0x188</load_address>
         <run_address>0x188</run_address>
         <size>0x2381</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_str</name>
         <load_address>0x2509</load_address>
         <run_address>0x2509</run_address>
         <size>0x165</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_str</name>
         <load_address>0x266e</load_address>
         <run_address>0x266e</run_address>
         <size>0x3bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_str</name>
         <load_address>0x2a2a</load_address>
         <run_address>0x2a2a</run_address>
         <size>0x60d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_str</name>
         <load_address>0x3037</load_address>
         <run_address>0x3037</run_address>
         <size>0x494</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_str</name>
         <load_address>0x34cb</load_address>
         <run_address>0x34cb</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-149">
         <name>.debug_str</name>
         <load_address>0x3642</load_address>
         <run_address>0x3642</run_address>
         <size>0xc45</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-147">
         <name>.debug_str</name>
         <load_address>0x4287</load_address>
         <run_address>0x4287</run_address>
         <size>0xced</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-145">
         <name>.debug_str</name>
         <load_address>0x4f74</load_address>
         <run_address>0x4f74</run_address>
         <size>0x107f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_str</name>
         <load_address>0x5ff3</load_address>
         <run_address>0x5ff3</run_address>
         <size>0x22d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_str</name>
         <load_address>0x6220</load_address>
         <run_address>0x6220</run_address>
         <size>0x337</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_str</name>
         <load_address>0x6557</load_address>
         <run_address>0x6557</run_address>
         <size>0xfd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_str</name>
         <load_address>0x6654</load_address>
         <run_address>0x6654</run_address>
         <size>0x1a3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_str</name>
         <load_address>0x67f7</load_address>
         <run_address>0x67f7</run_address>
         <size>0x150</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.debug_str</name>
         <load_address>0x6947</load_address>
         <run_address>0x6947</run_address>
         <size>0xf1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ed"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.debug_str</name>
         <load_address>0x6a38</load_address>
         <run_address>0x6a38</run_address>
         <size>0x1a3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_frame</name>
         <load_address>0x24</load_address>
         <run_address>0x24</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_frame</name>
         <load_address>0x130</load_address>
         <run_address>0x130</run_address>
         <size>0x204</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_frame</name>
         <load_address>0x334</load_address>
         <run_address>0x334</run_address>
         <size>0xb0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_frame</name>
         <load_address>0x3e4</load_address>
         <run_address>0x3e4</run_address>
         <size>0xc8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_frame</name>
         <load_address>0x4ac</load_address>
         <run_address>0x4ac</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-120">
         <name>.debug_frame</name>
         <load_address>0x4cc</load_address>
         <run_address>0x4cc</run_address>
         <size>0x234</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_frame</name>
         <load_address>0x700</load_address>
         <run_address>0x700</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.debug_frame</name>
         <load_address>0x8b8</load_address>
         <run_address>0x8b8</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_frame</name>
         <load_address>0x9e4</load_address>
         <run_address>0x9e4</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_frame</name>
         <load_address>0xa74</load_address>
         <run_address>0xa74</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_frame</name>
         <load_address>0xb74</load_address>
         <run_address>0xb74</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_frame</name>
         <load_address>0xb94</load_address>
         <run_address>0xb94</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_frame</name>
         <load_address>0xbcc</load_address>
         <run_address>0xbcc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_frame</name>
         <load_address>0xbfc</load_address>
         <run_address>0xbfc</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ed"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_frame</name>
         <load_address>0xc1c</load_address>
         <run_address>0xc1c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_line</name>
         <load_address>0x1b5</load_address>
         <run_address>0x1b5</run_address>
         <size>0x709</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_line</name>
         <load_address>0x8be</load_address>
         <run_address>0x8be</run_address>
         <size>0xc1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_line</name>
         <load_address>0x97f</load_address>
         <run_address>0x97f</run_address>
         <size>0x136f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_line</name>
         <load_address>0x1cee</load_address>
         <run_address>0x1cee</run_address>
         <size>0x32dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_line</name>
         <load_address>0x4fca</load_address>
         <run_address>0x4fca</run_address>
         <size>0x52c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_line</name>
         <load_address>0x54f6</load_address>
         <run_address>0x54f6</run_address>
         <size>0x179</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-121">
         <name>.debug_line</name>
         <load_address>0x566f</load_address>
         <run_address>0x566f</run_address>
         <size>0xc1b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_line</name>
         <load_address>0x628a</load_address>
         <run_address>0x628a</run_address>
         <size>0xa18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.debug_line</name>
         <load_address>0x6ca2</load_address>
         <run_address>0x6ca2</run_address>
         <size>0x983</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x7625</load_address>
         <run_address>0x7625</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_line</name>
         <load_address>0x7801</load_address>
         <run_address>0x7801</run_address>
         <size>0x403</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_line</name>
         <load_address>0x7c04</load_address>
         <run_address>0x7c04</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_line</name>
         <load_address>0x7c42</load_address>
         <run_address>0x7c42</run_address>
         <size>0x106</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_line</name>
         <load_address>0x7d48</load_address>
         <run_address>0x7d48</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_line</name>
         <load_address>0x7daf</load_address>
         <run_address>0x7daf</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ed"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_line</name>
         <load_address>0x7df0</load_address>
         <run_address>0x7df0</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_line</name>
         <load_address>0x7f5d</load_address>
         <run_address>0x7f5d</run_address>
         <size>0xc1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_line</name>
         <load_address>0x801e</load_address>
         <run_address>0x801e</run_address>
         <size>0xe8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-100"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_line</name>
         <load_address>0x8106</load_address>
         <run_address>0x8106</run_address>
         <size>0xc8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.debug_line</name>
         <load_address>0x81ce</load_address>
         <run_address>0x81ce</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_line</name>
         <load_address>0x828e</load_address>
         <run_address>0x828e</run_address>
         <size>0xd9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_line</name>
         <load_address>0x8367</load_address>
         <run_address>0x8367</run_address>
         <size>0xca</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_line</name>
         <load_address>0x8431</load_address>
         <run_address>0x8431</run_address>
         <size>0xbd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x70</load_address>
         <run_address>0x70</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_ranges</name>
         <load_address>0x88</load_address>
         <run_address>0x88</run_address>
         <size>0x150</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_ranges</name>
         <load_address>0x1d8</load_address>
         <run_address>0x1d8</run_address>
         <size>0x1940</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_ranges</name>
         <load_address>0x1b18</load_address>
         <run_address>0x1b18</run_address>
         <size>0xd0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.debug_ranges</name>
         <load_address>0x1be8</load_address>
         <run_address>0x1be8</run_address>
         <size>0x390</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-116">
         <name>.debug_ranges</name>
         <load_address>0x1f78</load_address>
         <run_address>0x1f78</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.debug_ranges</name>
         <load_address>0x2120</load_address>
         <run_address>0x2120</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_ranges</name>
         <load_address>0x22c8</load_address>
         <run_address>0x22c8</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_ranges</name>
         <load_address>0x2310</load_address>
         <run_address>0x2310</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_ranges</name>
         <load_address>0x2358</load_address>
         <run_address>0x2358</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_ranges</name>
         <load_address>0x2370</load_address>
         <run_address>0x2370</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_ranges</name>
         <load_address>0x2388</load_address>
         <run_address>0x2388</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-111"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-fd"/>
      </object_component>
      <object_component id="oc-135">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ff"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-100"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-102"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-104"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_aranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-107"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_aranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-10c"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0x3a50</load_address>
         <run_address>0x3a50</run_address>
         <size>0x3110</size>
         <contents>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-5f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x6b60</load_address>
         <run_address>0x6b60</run_address>
         <size>0x18</size>
         <contents>
            <object_component_ref idref="oc-18f"/>
            <object_component_ref idref="oc-18d"/>
            <object_component_ref idref="oc-18e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x3988</size>
         <contents>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-ab"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-157"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x28</size>
         <contents>
            <object_component_ref idref="oc-d7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-191"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14e" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14f" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-150" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-151" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-152" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-153" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-155" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-171" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x5b8d</size>
         <contents>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-14c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-173" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1638</size>
         <contents>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-193"/>
         </contents>
      </logical_group>
      <logical_group id="lg-175" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x10cd6</size>
         <contents>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-8c"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-192"/>
         </contents>
      </logical_group>
      <logical_group id="lg-177" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6bdb</size>
         <contents>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-14b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-179" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc4c</size>
         <contents>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-130"/>
         </contents>
      </logical_group>
      <logical_group id="lg-17b" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x84ee</size>
         <contents>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-131"/>
         </contents>
      </logical_group>
      <logical_group id="lg-17d" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x23a0</size>
         <contents>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-12f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-187" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xe0</size>
         <contents>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-ec"/>
         </contents>
      </logical_group>
      <logical_group id="lg-190" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-19c" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3a48</size>
         <flags>0x4</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-7"/>
         </contents>
      </load_segment>
      <load_segment id="lg-19d" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <load_address>0x3a50</load_address>
         <run_address>0x3a50</run_address>
         <size>0x3128</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-19e" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20200000</run_address>
         <size>0x28</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-19f" display="no" color="cyan">
         <name>SEGMENT_3</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x6b70</used_space>
         <unused_space>0x19490</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x3988</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <available_space>
               <start_address>0x3a48</start_address>
               <size>0x8</size>
            </available_space>
            <allocated_space>
               <start_address>0x3a50</start_address>
               <size>0x3110</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x6b60</start_address>
               <size>0x18</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x6b78</start_address>
               <size>0x19488</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x228</used_space>
         <unused_space>0x7dd8</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-153"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-155"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x28</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200028</start_address>
               <size>0x7dd8</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x6b60</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x28</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x6b6c</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x6b74</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x6b74</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x6b68</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x6b6c</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-43">
         <name>main</name>
         <value>0x5aad</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-6a">
         <name>SYSCFG_DL_init</name>
         <value>0x6a9d</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-6b">
         <name>SYSCFG_DL_initPower</name>
         <value>0x6905</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-6c">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x674d</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-6d">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x6689</value>
         <object_component_ref idref="oc-d2"/>
      </symbol>
      <symbol id="sm-6e">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x66ed</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-6f">
         <name>SYSCFG_DL_SPI_LCD_init</name>
         <value>0x679d</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-70">
         <name>SYSCFG_DL_SYSCTL_CLK_init</name>
         <value>0x6a75</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-71">
         <name>gSPI_LCDBackup</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-7c">
         <name>Default_Handler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7d">
         <name>Reset_Handler</name>
         <value>0x6b4d</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-7e">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-7f">
         <name>NMI_Handler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-80">
         <name>HardFault_Handler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>SVC_Handler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>PendSV_Handler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>SysTick_Handler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>GROUP0_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-85">
         <name>GROUP1_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-86">
         <name>TIMG8_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>UART3_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>ADC0_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>ADC1_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>CANFD0_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>DAC0_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8c">
         <name>SPI0_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>SPI1_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>UART1_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8f">
         <name>UART2_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-90">
         <name>UART0_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-91">
         <name>TIMG0_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-92">
         <name>TIMG6_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-93">
         <name>TIMA0_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-94">
         <name>TIMA1_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-95">
         <name>TIMG7_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-96">
         <name>TIMG12_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-97">
         <name>I2C0_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-98">
         <name>I2C1_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-99">
         <name>AES_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9a">
         <name>RTC_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9b">
         <name>DMA_IRQHandler</name>
         <value>0x49fb</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-be">
         <name>LCD_Fill</name>
         <value>0x4dfd</value>
         <object_component_ref idref="oc-8a"/>
      </symbol>
      <symbol id="sm-bf">
         <name>LCD_ShowChinese</name>
         <value>0x6401</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-c0">
         <name>LCD_ShowChinese16x16</name>
         <value>0x5339</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-c1">
         <name>LCD_ShowChinese12x12</name>
         <value>0x50bd</value>
         <object_component_ref idref="oc-e6"/>
      </symbol>
      <symbol id="sm-c2">
         <name>LCD_ShowChinese24x24</name>
         <value>0x55b5</value>
         <object_component_ref idref="oc-e7"/>
      </symbol>
      <symbol id="sm-c3">
         <name>LCD_ShowChinese32x32</name>
         <value>0x5831</value>
         <object_component_ref idref="oc-e8"/>
      </symbol>
      <symbol id="sm-c4">
         <name>tfont12</name>
         <value>0x395a</value>
         <object_component_ref idref="oc-126"/>
      </symbol>
      <symbol id="sm-c5">
         <name>tfont16</name>
         <value>0x38b0</value>
         <object_component_ref idref="oc-125"/>
      </symbol>
      <symbol id="sm-c6">
         <name>tfont24</name>
         <value>0x373e</value>
         <object_component_ref idref="oc-127"/>
      </symbol>
      <symbol id="sm-c7">
         <name>tfont32</name>
         <value>0x34b4</value>
         <object_component_ref idref="oc-128"/>
      </symbol>
      <symbol id="sm-c8">
         <name>LCD_ShowChar</name>
         <value>0x49fd</value>
         <object_component_ref idref="oc-e9"/>
      </symbol>
      <symbol id="sm-c9">
         <name>ascii_2412</name>
         <value>0x1880</value>
         <object_component_ref idref="oc-129"/>
      </symbol>
      <symbol id="sm-ca">
         <name>ascii_1608</name>
         <value>0x2a50</value>
         <object_component_ref idref="oc-12a"/>
      </symbol>
      <symbol id="sm-cb">
         <name>ascii_1206</name>
         <value>0x3040</value>
         <object_component_ref idref="oc-12b"/>
      </symbol>
      <symbol id="sm-cc">
         <name>ascii_3216</name>
         <value>0xc0</value>
         <object_component_ref idref="oc-12c"/>
      </symbol>
      <symbol id="sm-cd">
         <name>LCD_ShowString</name>
         <value>0x67e9</value>
         <object_component_ref idref="oc-97"/>
      </symbol>
      <symbol id="sm-ce">
         <name>LCD_ShowIntNum</name>
         <value>0x6219</value>
         <object_component_ref idref="oc-98"/>
      </symbol>
      <symbol id="sm-cf">
         <name>LCD_ShowFloatNum1</name>
         <value>0x60e5</value>
         <object_component_ref idref="oc-99"/>
      </symbol>
      <symbol id="sm-e1">
         <name>LCD_WR_DATA</name>
         <value>0x65b5</value>
         <object_component_ref idref="oc-e0"/>
      </symbol>
      <symbol id="sm-e2">
         <name>LCD_Address_Set</name>
         <value>0x5d25</value>
         <object_component_ref idref="oc-df"/>
      </symbol>
      <symbol id="sm-e3">
         <name>LCD_Init</name>
         <value>0x3a51</value>
         <object_component_ref idref="oc-84"/>
      </symbol>
      <symbol id="sm-ee">
         <name>delay_ms</name>
         <value>0x6b29</value>
         <object_component_ref idref="oc-90"/>
      </symbol>
      <symbol id="sm-ef">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f0">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f1">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f2">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f3">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f4">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f5">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f6">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f7">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-100">
         <name>DL_Common_delayCycles</name>
         <value>0x6b39</value>
         <object_component_ref idref="oc-80"/>
      </symbol>
      <symbol id="sm-10d">
         <name>DL_SPI_init</name>
         <value>0x687d</value>
         <object_component_ref idref="oc-122"/>
      </symbol>
      <symbol id="sm-10e">
         <name>DL_SPI_setClockConfig</name>
         <value>0x6b03</value>
         <object_component_ref idref="oc-11c"/>
      </symbol>
      <symbol id="sm-11b">
         <name>DL_UART_init</name>
         <value>0x6835</value>
         <object_component_ref idref="oc-119"/>
      </symbol>
      <symbol id="sm-11c">
         <name>DL_UART_setClockConfig</name>
         <value>0x6b15</value>
         <object_component_ref idref="oc-113"/>
      </symbol>
      <symbol id="sm-12d">
         <name>DL_SYSCTL_configSYSPLL</name>
         <value>0x6325</value>
         <object_component_ref idref="oc-110"/>
      </symbol>
      <symbol id="sm-12e">
         <name>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</name>
         <value>0x68c1</value>
         <object_component_ref idref="oc-111"/>
      </symbol>
      <symbol id="sm-12f">
         <name>DL_SYSCTL_setHFCLKSourceHFXTParams</name>
         <value>0x6625</value>
         <object_component_ref idref="oc-10a"/>
      </symbol>
      <symbol id="sm-13c">
         <name>_c_int00_noargs</name>
         <value>0x6ac5</value>
         <object_component_ref idref="oc-4f"/>
      </symbol>
      <symbol id="sm-13d">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-149">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x69c5</value>
         <object_component_ref idref="oc-b7"/>
      </symbol>
      <symbol id="sm-151">
         <name>_system_pre_init</name>
         <value>0x6b51</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-15c">
         <name>__TI_zero_init_nomemset</name>
         <value>0x6aed</value>
         <object_component_ref idref="oc-46"/>
      </symbol>
      <symbol id="sm-16c">
         <name>abort</name>
         <value>0x6b43</value>
         <object_component_ref idref="oc-b0"/>
      </symbol>
      <symbol id="sm-184">
         <name>HOSTexit</name>
         <value>0x6b49</value>
         <object_component_ref idref="oc-f9"/>
      </symbol>
      <symbol id="sm-185">
         <name>C$$EXIT</name>
         <value>0x6b48</value>
         <object_component_ref idref="oc-f9"/>
      </symbol>
      <symbol id="sm-19a">
         <name>__aeabi_dadd</name>
         <value>0x5f5b</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-19b">
         <name>__adddf3</name>
         <value>0x5f5b</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-19c">
         <name>__aeabi_dsub</name>
         <value>0x5f51</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-19d">
         <name>__subdf3</name>
         <value>0x5f51</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-1a3">
         <name>__muldsi3</name>
         <value>0x6a01</value>
         <object_component_ref idref="oc-133"/>
      </symbol>
      <symbol id="sm-1a9">
         <name>__aeabi_fmul</name>
         <value>0x64b5</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-1aa">
         <name>__mulsf3</name>
         <value>0x64b5</value>
         <object_component_ref idref="oc-ee"/>
      </symbol>
      <symbol id="sm-1b0">
         <name>__aeabi_f2d</name>
         <value>0x6985</value>
         <object_component_ref idref="oc-9a"/>
      </symbol>
      <symbol id="sm-1b1">
         <name>__extendsfdf2</name>
         <value>0x6985</value>
         <object_component_ref idref="oc-9a"/>
      </symbol>
      <symbol id="sm-1b7">
         <name>__aeabi_f2iz</name>
         <value>0x6a3d</value>
         <object_component_ref idref="oc-f2"/>
      </symbol>
      <symbol id="sm-1b8">
         <name>__fixsfsi</name>
         <value>0x6a3d</value>
         <object_component_ref idref="oc-f2"/>
      </symbol>
      <symbol id="sm-1bf">
         <name>__aeabi_d2f</name>
         <value>0x6541</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-1c0">
         <name>__truncdfsf2</name>
         <value>0x6541</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-1c6">
         <name>__aeabi_uidiv</name>
         <value>0x6945</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-1c7">
         <name>__aeabi_uidivmod</name>
         <value>0x6945</value>
         <object_component_ref idref="oc-ea"/>
      </symbol>
      <symbol id="sm-1d2">
         <name>__aeabi_idiv0</name>
         <value>0x60e3</value>
         <object_component_ref idref="oc-12d"/>
      </symbol>
      <symbol id="sm-1d4">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d6">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d7">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
