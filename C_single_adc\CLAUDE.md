# C_single_adc 项目记忆文件

## 项目概述
这是一个基于 Texas Instruments MSPM0G3507 微控制器的 ADC 单次转换示例项目。项目使用 Code Composer Studio (CCS) 开发环境，通过定时器触发 ADC 采样，并收集模拟信号数据。

## 硬件平台
- **微控制器**: MSPM0G3507 (ARM Cortex-M0+)
- **开发板**: LP-MSPM0G3507 LaunchPad
- **调试器**: Texas Instruments XDS110 USB Debug Probe
- **主频**: 32MHz

## 关键文件说明

### 主要源代码文件
- **`adc12_single_conversion.c`**: 主程序文件
  - 实现 ADC 中断处理和数据采集
  - 定义采样缓冲区和电压转换逻辑
  - 位置: 项目根目录

### 配置文件
- **`adc12_single_conversion.syscfg`**: SysConfig 配置文件
  - ADC12_0 实例配置 (通道2, PA25引脚)
  - 定时器配置 (100us 周期)
  - DMA 配置
  - 中断设置
  
- **`ti_msp_dl_config.h`**: 自动生成的驱动库配置头文件
  - 定义所有外设的句柄和常量
  - 包含初始化函数声明
  - 位置: Debug/ 目录

### 调试配置
- **`targetConfigs/MSPM0G3507.ccxml`**: CCS 目标配置文件
  - XDS110 调试器配置
  - SWD 模式设置
  - JTAG 时钟配置 (1MHz)

## 功能特性

### 1. ADC 配置
- **通道**: ADC12 通道2 (PA25引脚)
- **参考电压**: VDDA (3.3V)
- **采样时钟分频**: 8分频
- **采样时间**: 125µs
- **分辨率**: 12位 (0-4095)
- **触发源**: 定时器事件触发
- **工作模式**: 重复模式，支持DMA

### 2. 定时器配置
- **定时器**: TIMA0
- **模式**: 周期模式
- **周期**: 100µs
- **功能**: 生成 ADC 触发事件

### 3. 数据采集
- **采样数**: 1024个样本
- **数据类型**: 16位无符号整数转换为浮点电压值
- **电压范围**: 0 - 2.5V (使用内部2.5V基准)
- **存储**: 浮点数组 `gAdcWaveform[1024]`

## 代码结构分析

### 全局变量 (`adc12_single_conversion.c:36-51`)
```c
volatile bool gCheckADC;                    // ADC 状态标志
volatile uint16_t gAdcResult;               // ADC 原始结果
volatile float gAdcVoltage;                 // 转换后的电压值
volatile float gAdcWaveform[1024];          // 波形数据缓冲区  
volatile uint16_t gSampleIndex = 0;         // 当前采样索引
```

### 主要常量定义
```c
#define ADC_VREF      (2.5f)    // ADC 参考电压
#define ADC_MAX_VAL   (4095.0f) // 12位 ADC 最大值
#define SAMPLES_TO_COLLECT (1024) // 采样点数
```

### 主函数 (`adc12_single_conversion.c:55-65`)
- 初始化系统配置
- 使能 ADC 中断
- 启动定时器
- 进入低功耗等待模式

### ADC 中断处理 (`adc12_single_conversion.c:67-90`)
- 读取 ADC 转换结果
- 执行电压转换: `voltage = (result / 4095.0) * 2.5`
- 存储到波形缓冲区
- 管理采样索引

## 引脚分配

| 功能 | 引脚 | 说明 |
|------|------|------|
| ADC输入 | PA25 | ADC12 通道2，模拟输入 |  
| LED控制 | PA0 | 开漏输出，可选连接LED1 |
| 调试时钟 | PA20 | SWD 调试接口 |
| 调试数据 | PA19 | SWD 调试接口 |

## 构建系统

### 编译环境
- **IDE**: Code Composer Studio
- **编译器**: TI CLANG
- **SDK**: MSPM0 SDK v2.05.01.01
- **设备支持**: MSPM0G350X 系列

### 构建输出 (Debug/ 目录)
- **`.out`**: 可执行文件
- **`.map`**: 内存映射文件  
- **`.o/.d`**: 目标文件和依赖文件
- **`makefile`**: 自动生成的构建脚本

## 使用说明

### 硬件连接
1. 将模拟信号连接到 J1_2 (PA25)
2. 确保输入电压在 0-VCC 范围内
3. 可选：连接 LED1 到 PA0 用于状态指示

### 软件操作
1. 在 CCS 中加载项目
2. 编译并下载到目标板
3. 运行程序开始采集
4. 通过调试器查看 `gAdcWaveform` 数组获取采样数据

## 开发注意事项

### 低功耗建议
- 使用 SysConfig 配置未使用引脚为 GPIO 输出低电平或带内部上下拉的输入
- 参考 LP-MSPM0G3507 用户指南进行跳线配置

### 设备迁移
- 项目基于 MSPM0G3507 开发
- 迁移到其他 MSPM0 设备时参考 CCS 用户指南

### 调试配置
- ADC 引脚默认设置为模拟模式，无需额外配置
- 使用 XDS110 调试器进行在线调试
- 支持 SWD 接口调试

## 项目状态
- **开发状态**: 完整可用的示例代码
- **测试状态**: 基于 TI 官方示例，经过验证
- **维护状态**: 使用标准 TI DriverLib，便于维护和升级