#include "button_control.h"
#include "ti_msp_dl_config.h"
#include "BSP/AD9850/ad9850.h"

// 全局按键控制实例指针（避免重复定义）
static button_control_t* g_button_ctrl_ptr = NULL;

// 简单的延时函数（用于防抖）- 修复版本
static void delay_ms_simple(uint32_t ms) {
    // 使用更短的延时，避免在中断中阻塞太久
    volatile uint32_t count = ms * 1000;  // 减少延时时间
    while (count--) {
        __asm("nop");
    }
}

// 初始化按键控制
void button_control_init(button_control_t* ctrl, uint32_t initial_freq) {
    if (ctrl == NULL) return;

    ctrl->current_freq = initial_freq;
    ctrl->last_button_time = 0;
    ctrl->freq_changed = false;

    // 保存指针引用，避免数据拷贝
    g_button_ctrl_ptr = ctrl;
}

// 频率增加
void button_control_freq_up(button_control_t* ctrl) {
    if (ctrl == NULL) return;
    
    if (ctrl->current_freq < FREQ_MAX_HZ) {
        ctrl->current_freq += FREQ_STEP_HZ;
        ctrl->freq_changed = true;
        
        // 立即更新DDS输出
        dds_set(ctrl->current_freq);
    }
}

// 频率减少
void button_control_freq_down(button_control_t* ctrl) {
    if (ctrl == NULL) return;
    
    if (ctrl->current_freq > FREQ_MIN_HZ) {
        ctrl->current_freq -= FREQ_STEP_HZ;
        ctrl->freq_changed = true;
        
        // 立即更新DDS输出
        dds_set(ctrl->current_freq);
    }
}

// 获取当前频率
uint32_t button_control_get_freq(button_control_t* ctrl) {
    if (ctrl == NULL) return 0;
    return ctrl->current_freq;
}

// 检查频率是否发生变化
bool button_control_freq_changed(button_control_t* ctrl) {
    if (ctrl == NULL) return false;
    return ctrl->freq_changed;
}

// 清除变化标志
void button_control_clear_change_flag(button_control_t* ctrl) {
    if (ctrl == NULL) return;
    ctrl->freq_changed = false;
}

// 新增：轮询方式按键检测（更稳定的实现）
void button_control_poll(button_control_t* ctrl) {
    if (ctrl == NULL) return;

    static uint32_t last_poll_time = 0;
    static bool freq_up_pressed = false;
    static bool freq_down_pressed = false;

    // 简单的时间计数（基于调用次数估算）
    static uint32_t poll_counter = 0;
    poll_counter++;

    // 大约每200次调用检查一次（假设主循环100ms一次，约20秒检查一次按键）
    // 实际上我们希望更频繁，所以改为每20次调用检查一次
    if (poll_counter % 20 != 0) {
        return;
    }

    // 读取按键状态（按下为低电平）
    bool freq_up_current = (DL_GPIO_readPins(BUTTONS_PORT, BUTTONS_FREQ_UP_PIN) == 0);
    bool freq_down_current = (DL_GPIO_readPins(BUTTONS_PORT, BUTTONS_FREQ_DOWN_PIN) == 0);

    // 检测按键按下边沿（防抖处理）
    if (freq_up_current && !freq_up_pressed) {
        button_control_freq_up(ctrl);
        freq_up_pressed = true;
    } else if (!freq_up_current) {
        freq_up_pressed = false;
    }

    if (freq_down_current && !freq_down_pressed) {
        button_control_freq_down(ctrl);
        freq_down_pressed = true;
    } else if (!freq_down_current) {
        freq_down_pressed = false;
    }
}

// GPIO中断服务函数（处理所有GPIOB中断）- 修复版本（已禁用）
void GPIOB_IRQHandler_DISABLED(void) {
    uint32_t pending = DL_GPIO_getPendingInterrupt(BUTTONS_PORT);

    // 立即清除所有中断标志，避免重复触发
    DL_GPIO_clearInterruptStatus(BUTTONS_PORT, pending);

    // 检查频率增加按键（使用指针避免数据问题）
    if (pending & BUTTONS_FREQ_UP_PIN) {
        // 直接检查按键状态，不在中断中延时
        if (DL_GPIO_readPins(BUTTONS_PORT, BUTTONS_FREQ_UP_PIN) == 0) {
            if (g_button_ctrl_ptr != NULL) {
                button_control_freq_up(g_button_ctrl_ptr);
            }
        }
    }

    // 检查频率减少按键
    if (pending & BUTTONS_FREQ_DOWN_PIN) {
        if (DL_GPIO_readPins(BUTTONS_PORT, BUTTONS_FREQ_DOWN_PIN) == 0) {
            if (g_button_ctrl_ptr != NULL) {
                button_control_freq_down(g_button_ctrl_ptr);
            }
        }
    }
}
