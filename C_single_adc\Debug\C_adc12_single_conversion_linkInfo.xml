<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\bin\tiarmlnk -IG:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib -o C_adc12_single_conversion.out -mC_adc12_single_conversion.map -iC:/TI/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/C_adc12_single_conversion -iC:/Users/<USER>/workspace_ccstheia/C_adc12_single_conversion/Debug/syscfg -iG:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=C_adc12_single_conversion_linkInfo.xml --rom_model ./adc12_single_conversion.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x68763ec3</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\C_adc12_single_conversion\Debug\C_adc12_single_conversion.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x731</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\C_adc12_single_conversion\Debug\.\</path>
         <kind>object</kind>
         <file>adc12_single_conversion.o</file>
         <name>adc12_single_conversion.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\C_adc12_single_conversion\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\C_adc12_single_conversion\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>C:\Users\<USER>\workspace_ccstheia\C_adc12_single_conversion\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_adc12.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_dma.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-2b">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d5">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-91">
         <name>.text:memcpy</name>
         <load_address>0x1a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a8</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x242</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x242</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-63">
         <name>.text.__mulsf3</name>
         <load_address>0x244</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x244</run_address>
         <size>0x8c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-c1">
         <name>.text.SYSCFG_DL_ADC12_0_init</name>
         <load_address>0x2d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2d0</run_address>
         <size>0x88</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.text.__divsf3</name>
         <load_address>0x358</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x358</run_address>
         <size>0x82</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x3da</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3da</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x3dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3dc</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.ADC0_IRQHandler</name>
         <load_address>0x458</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x458</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text:memset</name>
         <load_address>0x4bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4bc</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.text.DL_DMA_initChannel</name>
         <load_address>0x520</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x520</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.text.SYSCFG_DL_TIMER_0_init</name>
         <load_address>0x56c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x56c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.text.DL_ADC12_setClockConfig</name>
         <load_address>0x5b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5b8</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x5f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5f8</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x638</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x638</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-87">
         <name>.text.__muldsi3</name>
         <load_address>0x674</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x674</run_address>
         <size>0x3a</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-be">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x6b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6b0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-96">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x6e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6e0</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.text.__floatunsisf</name>
         <load_address>0x708</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x708</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x730</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x730</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.text.main</name>
         <load_address>0x758</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x758</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x77c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x77c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text.SYSCFG_DL_DMA_CH0_init</name>
         <load_address>0x798</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x798</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x7b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7b0</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-51">
         <name>.text:decompress:ZI</name>
         <load_address>0x7c2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7c2</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-71">
         <name>.text.__aeabi_memclr</name>
         <load_address>0x7d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7d4</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x7e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7e0</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.text.SYSCFG_DL_DMA_init</name>
         <load_address>0x7ea</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7ea</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-44">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x7f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7f4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.text:abort</name>
         <load_address>0x7fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7fc</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-c6">
         <name>.text.HOSTexit</name>
         <load_address>0x802</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x802</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x806</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x806</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-76">
         <name>.text._system_pre_init</name>
         <load_address>0x80a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x80a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-145">
         <name>__TI_handler_table</name>
         <load_address>0x848</load_address>
         <readonly>true</readonly>
         <run_address>0x848</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-148">
         <name>.cinit..bss.load</name>
         <load_address>0x854</load_address>
         <readonly>true</readonly>
         <run_address>0x854</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-147">
         <name>.cinit..data.load</name>
         <load_address>0x85c</load_address>
         <readonly>true</readonly>
         <run_address>0x85c</run_address>
         <size>0x6</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-146">
         <name>__TI_cinit_table</name>
         <load_address>0x864</load_address>
         <readonly>true</readonly>
         <run_address>0x864</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-102">
         <name>.rodata.gDMA_CH0Config</name>
         <load_address>0x810</load_address>
         <readonly>true</readonly>
         <run_address>0x810</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.rodata.gTIMER_0TimerConfig</name>
         <load_address>0x828</load_address>
         <readonly>true</readonly>
         <run_address>0x828</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.rodata.gADC12_0ClockConfig</name>
         <load_address>0x83c</load_address>
         <readonly>true</readonly>
         <run_address>0x83c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.rodata.gTIMER_0ClockConfig</name>
         <load_address>0x844</load_address>
         <readonly>true</readonly>
         <run_address>0x844</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-69">
         <name>.data.gSampleIndex</name>
         <load_address>0x202010c2</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202010c2</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-67">
         <name>.common:gAdcResult</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010c0</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-68">
         <name>.common:gAdcVoltage</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010bc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-6a">
         <name>.common:gAdcWaveform</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x1000</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-c3">
         <name>.common:gTIMER_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20201000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1a3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_abbrev</name>
         <load_address>0x1a3</load_address>
         <run_address>0x1a3</run_address>
         <size>0x211</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_abbrev</name>
         <load_address>0x3b4</load_address>
         <run_address>0x3b4</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_abbrev</name>
         <load_address>0x421</load_address>
         <run_address>0x421</run_address>
         <size>0x171</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.debug_abbrev</name>
         <load_address>0x592</load_address>
         <run_address>0x592</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_abbrev</name>
         <load_address>0x5f4</load_address>
         <run_address>0x5f4</run_address>
         <size>0x180</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_abbrev</name>
         <load_address>0x774</load_address>
         <run_address>0x774</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0x9fa</load_address>
         <run_address>0x9fa</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_abbrev</name>
         <load_address>0xaa9</load_address>
         <run_address>0xaa9</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_abbrev</name>
         <load_address>0xc19</load_address>
         <run_address>0xc19</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_abbrev</name>
         <load_address>0xc52</load_address>
         <run_address>0xc52</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_abbrev</name>
         <load_address>0xd14</load_address>
         <run_address>0xd14</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_abbrev</name>
         <load_address>0xd84</load_address>
         <run_address>0xd84</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_abbrev</name>
         <load_address>0xe11</load_address>
         <run_address>0xe11</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_abbrev</name>
         <load_address>0xea9</load_address>
         <run_address>0xea9</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_abbrev</name>
         <load_address>0xed5</load_address>
         <run_address>0xed5</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_abbrev</name>
         <load_address>0xefc</load_address>
         <run_address>0xefc</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_abbrev</name>
         <load_address>0xf23</load_address>
         <run_address>0xf23</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_abbrev</name>
         <load_address>0xf4a</load_address>
         <run_address>0xf4a</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_abbrev</name>
         <load_address>0xf71</load_address>
         <run_address>0xf71</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_abbrev</name>
         <load_address>0xf96</load_address>
         <run_address>0xf96</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_abbrev</name>
         <load_address>0xfbd</load_address>
         <run_address>0xfbd</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_abbrev</name>
         <load_address>0xfe2</load_address>
         <run_address>0xfe2</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.debug_abbrev</name>
         <load_address>0x1007</load_address>
         <run_address>0x1007</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xfb6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_info</name>
         <load_address>0xfb6</load_address>
         <run_address>0xfb6</run_address>
         <size>0x2a1e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x39d4</load_address>
         <run_address>0x39d4</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_info</name>
         <load_address>0x3a54</load_address>
         <run_address>0x3a54</run_address>
         <size>0x745</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_info</name>
         <load_address>0x4199</load_address>
         <run_address>0x4199</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_info</name>
         <load_address>0x420e</load_address>
         <run_address>0x420e</run_address>
         <size>0x6ea</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_info</name>
         <load_address>0x48f8</load_address>
         <run_address>0x48f8</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x7a6a</load_address>
         <run_address>0x7a6a</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_info</name>
         <load_address>0x7e8d</load_address>
         <run_address>0x7e8d</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_info</name>
         <load_address>0x85d1</load_address>
         <run_address>0x85d1</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_info</name>
         <load_address>0x8617</load_address>
         <run_address>0x8617</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0x87a9</load_address>
         <run_address>0x87a9</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_info</name>
         <load_address>0x886f</load_address>
         <run_address>0x886f</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_info</name>
         <load_address>0x89eb</load_address>
         <run_address>0x89eb</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_info</name>
         <load_address>0x8ae3</load_address>
         <run_address>0x8ae3</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-65">
         <name>.debug_info</name>
         <load_address>0x8b1e</load_address>
         <run_address>0x8b1e</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_info</name>
         <load_address>0x8cab</load_address>
         <run_address>0x8cab</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_info</name>
         <load_address>0x8e38</load_address>
         <run_address>0x8e38</run_address>
         <size>0x197</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_info</name>
         <load_address>0x8fcf</load_address>
         <run_address>0x8fcf</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_info</name>
         <load_address>0x9168</load_address>
         <run_address>0x9168</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_info</name>
         <load_address>0x931d</load_address>
         <run_address>0x931d</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_info</name>
         <load_address>0x94ac</load_address>
         <run_address>0x94ac</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_info</name>
         <load_address>0x97a6</load_address>
         <run_address>0x97a6</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.debug_info</name>
         <load_address>0x99ea</load_address>
         <run_address>0x99ea</run_address>
         <size>0xb9</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_ranges</name>
         <load_address>0x18</load_address>
         <run_address>0x18</run_address>
         <size>0x58</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_ranges</name>
         <load_address>0x70</load_address>
         <run_address>0x70</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.debug_ranges</name>
         <load_address>0x88</load_address>
         <run_address>0x88</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_ranges</name>
         <load_address>0xa0</load_address>
         <run_address>0xa0</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_ranges</name>
         <load_address>0x278</load_address>
         <run_address>0x278</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.debug_ranges</name>
         <load_address>0x2c0</load_address>
         <run_address>0x2c0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_ranges</name>
         <load_address>0x308</load_address>
         <run_address>0x308</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0x320</load_address>
         <run_address>0x320</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_ranges</name>
         <load_address>0x370</load_address>
         <run_address>0x370</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_ranges</name>
         <load_address>0x388</load_address>
         <run_address>0x388</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_ranges</name>
         <load_address>0x3b0</load_address>
         <run_address>0x3b0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_ranges</name>
         <load_address>0x3d8</load_address>
         <run_address>0x3d8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb82</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-c5">
         <name>.debug_str</name>
         <load_address>0xb82</load_address>
         <run_address>0xb82</run_address>
         <size>0x1d20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_str</name>
         <load_address>0x28a2</load_address>
         <run_address>0x28a2</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_str</name>
         <load_address>0x2a12</load_address>
         <run_address>0x2a12</run_address>
         <size>0x631</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.debug_str</name>
         <load_address>0x3043</load_address>
         <run_address>0x3043</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_str</name>
         <load_address>0x31b0</load_address>
         <run_address>0x31b0</run_address>
         <size>0x64a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_str</name>
         <load_address>0x37fa</load_address>
         <run_address>0x37fa</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x55c6</load_address>
         <run_address>0x55c6</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_str</name>
         <load_address>0x57eb</load_address>
         <run_address>0x57eb</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_str</name>
         <load_address>0x5b1a</load_address>
         <run_address>0x5b1a</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_str</name>
         <load_address>0x5c0f</load_address>
         <run_address>0x5c0f</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_str</name>
         <load_address>0x5daa</load_address>
         <run_address>0x5daa</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_str</name>
         <load_address>0x5f12</load_address>
         <run_address>0x5f12</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_str</name>
         <load_address>0x60e7</load_address>
         <run_address>0x60e7</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_str</name>
         <load_address>0x622f</load_address>
         <run_address>0x622f</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_frame</name>
         <load_address>0x38</load_address>
         <run_address>0x38</run_address>
         <size>0xf4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x12c</load_address>
         <run_address>0x12c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_frame</name>
         <load_address>0x15c</load_address>
         <run_address>0x15c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_frame</name>
         <load_address>0x1a8</load_address>
         <run_address>0x1a8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.debug_frame</name>
         <load_address>0x1c8</load_address>
         <run_address>0x1c8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_frame</name>
         <load_address>0x1f8</load_address>
         <run_address>0x1f8</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_frame</name>
         <load_address>0x600</load_address>
         <run_address>0x600</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_frame</name>
         <load_address>0x690</load_address>
         <run_address>0x690</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_frame</name>
         <load_address>0x790</load_address>
         <run_address>0x790</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_frame</name>
         <load_address>0x7b0</load_address>
         <run_address>0x7b0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0x7e8</load_address>
         <run_address>0x7e8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_frame</name>
         <load_address>0x810</load_address>
         <run_address>0x810</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_frame</name>
         <load_address>0x840</load_address>
         <run_address>0x840</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-c8">
         <name>.debug_frame</name>
         <load_address>0x870</load_address>
         <run_address>0x870</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2db</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_line</name>
         <load_address>0x2db</load_address>
         <run_address>0x2db</run_address>
         <size>0x535</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0x810</load_address>
         <run_address>0x810</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_line</name>
         <load_address>0x8c8</load_address>
         <run_address>0x8c8</run_address>
         <size>0x27f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_line</name>
         <load_address>0xb47</load_address>
         <run_address>0xb47</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_line</name>
         <load_address>0xcbf</load_address>
         <run_address>0xcbf</run_address>
         <size>0x248</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_line</name>
         <load_address>0xf07</load_address>
         <run_address>0xf07</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_line</name>
         <load_address>0x2675</load_address>
         <run_address>0x2675</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_line</name>
         <load_address>0x2851</load_address>
         <run_address>0x2851</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_line</name>
         <load_address>0x2d6b</load_address>
         <run_address>0x2d6b</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_line</name>
         <load_address>0x2da9</load_address>
         <run_address>0x2da9</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x2ea7</load_address>
         <run_address>0x2ea7</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x2f67</load_address>
         <run_address>0x2f67</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.debug_line</name>
         <load_address>0x312f</load_address>
         <run_address>0x312f</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-c7">
         <name>.debug_line</name>
         <load_address>0x3196</load_address>
         <run_address>0x3196</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_line</name>
         <load_address>0x31d7</load_address>
         <run_address>0x31d7</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_line</name>
         <load_address>0x32b7</load_address>
         <run_address>0x32b7</run_address>
         <size>0xdc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_line</name>
         <load_address>0x3393</load_address>
         <run_address>0x3393</run_address>
         <size>0xb4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_line</name>
         <load_address>0x3447</load_address>
         <run_address>0x3447</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-75">
         <name>.debug_line</name>
         <load_address>0x34eb</load_address>
         <run_address>0x34eb</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_line</name>
         <load_address>0x35a5</load_address>
         <run_address>0x35a5</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_line</name>
         <load_address>0x365e</load_address>
         <run_address>0x365e</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_line</name>
         <load_address>0x36fe</load_address>
         <run_address>0x36fe</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_loc</name>
         <load_address>0xb8</load_address>
         <run_address>0xb8</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.debug_loc</name>
         <load_address>0x17f</load_address>
         <run_address>0x17f</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-105">
         <name>.debug_loc</name>
         <load_address>0x192</load_address>
         <run_address>0x192</run_address>
         <size>0xd0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_loc</name>
         <load_address>0x262</load_address>
         <run_address>0x262</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_loc</name>
         <load_address>0x1c89</load_address>
         <run_address>0x1c89</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_loc</name>
         <load_address>0x1d61</load_address>
         <run_address>0x1d61</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_loc</name>
         <load_address>0x2185</load_address>
         <run_address>0x2185</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_loc</name>
         <load_address>0x22f1</load_address>
         <run_address>0x22f1</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_loc</name>
         <load_address>0x2360</load_address>
         <run_address>0x2360</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.debug_loc</name>
         <load_address>0x24c7</load_address>
         <run_address>0x24c7</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_aranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-88">
         <name>.debug_aranges</name>
         <load_address>0xa8</load_address>
         <run_address>0xa8</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-db"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_aranges</name>
         <load_address>0xc8</load_address>
         <run_address>0xc8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_aranges</name>
         <load_address>0xf0</load_address>
         <run_address>0xf0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x750</size>
         <contents>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-c1"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-c6"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-76"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x848</load_address>
         <run_address>0x848</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-146"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x810</load_address>
         <run_address>0x810</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-e2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-10f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202010c2</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-69"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x10c2</size>
         <contents>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-c3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-14a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-106" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-107" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-108" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-109" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10a" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10b" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-10d" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-129" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1016</size>
         <contents>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-14c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12b" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x9aa3</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-14b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12d" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x400</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-b7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12f" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6318</size>
         <contents>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-c5"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-ed"/>
         </contents>
      </logical_group>
      <logical_group id="lg-131" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x890</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-c8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-133" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x377e</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-c7"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-b8"/>
         </contents>
      </logical_group>
      <logical_group id="lg-135" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x24ed</size>
         <contents>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-a1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-13f" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x118</size>
         <contents>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-ba"/>
         </contents>
      </logical_group>
      <logical_group id="lg-149" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-155" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x878</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-156" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x10c4</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-157" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x878</used_space>
         <unused_space>0x1f788</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x750</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x810</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x848</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x878</start_address>
               <size>0x1f788</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x12c4</used_space>
         <unused_space>0x6d3c</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-10b"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-10d"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x10c2</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x202010c2</start_address>
               <size>0x2</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202010c4</start_address>
               <size>0x6d3c</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x854</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x10c2</run_size>
            <compression>zero_init</compression>
         </cprec>
         <cprec>
            <name>.data</name>
            <load_address>0x85c</load_address>
            <load_size>0x6</load_size>
            <run_address>0x202010c2</run_address>
            <run_size>0x2</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x864</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x874</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x874</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x848</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x854</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3f">
         <name>main</name>
         <value>0x759</value>
         <object_component_ref idref="oc-7a"/>
      </symbol>
      <symbol id="sm-40">
         <name>ADC0_IRQHandler</name>
         <value>0x459</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-41">
         <name>gAdcResult</name>
         <value>0x202010c0</value>
      </symbol>
      <symbol id="sm-42">
         <name>gAdcVoltage</name>
         <value>0x202010bc</value>
      </symbol>
      <symbol id="sm-43">
         <name>gSampleIndex</name>
         <value>0x202010c2</value>
         <object_component_ref idref="oc-69"/>
      </symbol>
      <symbol id="sm-44">
         <name>gAdcWaveform</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-6a">
         <name>SYSCFG_DL_init</name>
         <value>0x6e1</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-6b">
         <name>SYSCFG_DL_initPower</name>
         <value>0x5f9</value>
         <object_component_ref idref="oc-bc"/>
      </symbol>
      <symbol id="sm-6c">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x3db</value>
         <object_component_ref idref="oc-bd"/>
      </symbol>
      <symbol id="sm-6d">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x6b1</value>
         <object_component_ref idref="oc-be"/>
      </symbol>
      <symbol id="sm-6e">
         <name>SYSCFG_DL_TIMER_0_init</name>
         <value>0x56d</value>
         <object_component_ref idref="oc-c0"/>
      </symbol>
      <symbol id="sm-6f">
         <name>SYSCFG_DL_ADC12_0_init</name>
         <value>0x2d1</value>
         <object_component_ref idref="oc-c1"/>
      </symbol>
      <symbol id="sm-70">
         <name>SYSCFG_DL_DMA_init</name>
         <value>0x7eb</value>
         <object_component_ref idref="oc-c2"/>
      </symbol>
      <symbol id="sm-71">
         <name>gTIMER_0Backup</name>
         <value>0x20201000</value>
      </symbol>
      <symbol id="sm-72">
         <name>SYSCFG_DL_DMA_CH0_init</name>
         <value>0x799</value>
         <object_component_ref idref="oc-eb"/>
      </symbol>
      <symbol id="sm-7d">
         <name>Default_Handler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-7e">
         <name>Reset_Handler</name>
         <value>0x807</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-7f">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-80">
         <name>NMI_Handler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-81">
         <name>HardFault_Handler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-82">
         <name>SVC_Handler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-83">
         <name>PendSV_Handler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-84">
         <name>SysTick_Handler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-85">
         <name>GROUP0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-86">
         <name>GROUP1_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-87">
         <name>TIMG8_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-88">
         <name>UART3_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-89">
         <name>ADC1_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8a">
         <name>CANFD0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8b">
         <name>DAC0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8c">
         <name>SPI0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8d">
         <name>SPI1_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>UART1_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8f">
         <name>UART2_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-90">
         <name>UART0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-91">
         <name>TIMG0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-92">
         <name>TIMG6_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-93">
         <name>TIMA0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-94">
         <name>TIMA1_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-95">
         <name>TIMG7_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-96">
         <name>TIMG12_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-97">
         <name>I2C0_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-98">
         <name>I2C1_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-99">
         <name>AES_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9a">
         <name>RTC_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9b">
         <name>DMA_IRQHandler</name>
         <value>0x243</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9c">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-9d">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-9e">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-9f">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a0">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a1">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a2">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a3">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-a4">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-af">
         <name>DL_ADC12_setClockConfig</name>
         <value>0x5b9</value>
         <object_component_ref idref="oc-e4"/>
      </symbol>
      <symbol id="sm-b8">
         <name>DL_Common_delayCycles</name>
         <value>0x7e1</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-c2">
         <name>DL_DMA_initChannel</name>
         <value>0x521</value>
         <object_component_ref idref="oc-fe"/>
      </symbol>
      <symbol id="sm-d2">
         <name>DL_Timer_setClockConfig</name>
         <value>0x77d</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-d3">
         <name>DL_Timer_initTimerMode</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-e1"/>
      </symbol>
      <symbol id="sm-de">
         <name>_c_int00_noargs</name>
         <value>0x731</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-df">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-eb">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x639</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-f3">
         <name>_system_pre_init</name>
         <value>0x80b</value>
         <object_component_ref idref="oc-76"/>
      </symbol>
      <symbol id="sm-fe">
         <name>__TI_zero_init</name>
         <value>0x7c3</value>
         <object_component_ref idref="oc-51"/>
      </symbol>
      <symbol id="sm-107">
         <name>__TI_decompress_none</name>
         <value>0x7b1</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-112">
         <name>__TI_decompress_lzss</name>
         <value>0x3dd</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-11c">
         <name>abort</name>
         <value>0x7fd</value>
         <object_component_ref idref="oc-9b"/>
      </symbol>
      <symbol id="sm-126">
         <name>HOSTexit</name>
         <value>0x803</value>
         <object_component_ref idref="oc-c6"/>
      </symbol>
      <symbol id="sm-127">
         <name>C$$EXIT</name>
         <value>0x802</value>
         <object_component_ref idref="oc-c6"/>
      </symbol>
      <symbol id="sm-12d">
         <name>__aeabi_fmul</name>
         <value>0x245</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-12e">
         <name>__mulsf3</name>
         <value>0x245</value>
         <object_component_ref idref="oc-63"/>
      </symbol>
      <symbol id="sm-134">
         <name>__aeabi_fdiv</name>
         <value>0x359</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-135">
         <name>__divsf3</name>
         <value>0x359</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-13b">
         <name>__aeabi_ui2f</name>
         <value>0x709</value>
         <object_component_ref idref="oc-5b"/>
      </symbol>
      <symbol id="sm-13c">
         <name>__floatunsisf</name>
         <value>0x709</value>
         <object_component_ref idref="oc-5b"/>
      </symbol>
      <symbol id="sm-142">
         <name>__aeabi_memcpy</name>
         <value>0x7f5</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-143">
         <name>__aeabi_memcpy4</name>
         <value>0x7f5</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-144">
         <name>__aeabi_memcpy8</name>
         <value>0x7f5</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-14b">
         <name>__aeabi_memclr</name>
         <value>0x7d5</value>
         <object_component_ref idref="oc-71"/>
      </symbol>
      <symbol id="sm-14c">
         <name>__aeabi_memclr4</name>
         <value>0x7d5</value>
         <object_component_ref idref="oc-71"/>
      </symbol>
      <symbol id="sm-14d">
         <name>__aeabi_memclr8</name>
         <value>0x7d5</value>
         <object_component_ref idref="oc-71"/>
      </symbol>
      <symbol id="sm-153">
         <name>__muldsi3</name>
         <value>0x675</value>
         <object_component_ref idref="oc-87"/>
      </symbol>
      <symbol id="sm-16c">
         <name>memcpy</name>
         <value>0x1a9</value>
         <object_component_ref idref="oc-91"/>
      </symbol>
      <symbol id="sm-17b">
         <name>memset</name>
         <value>0x4bd</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-17c">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-17f">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-180">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
