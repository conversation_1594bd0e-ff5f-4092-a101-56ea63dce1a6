#include "adc_channel.h"
#include "lcd.h"
#include <string.h>
#include <math.h>

// 初始化ADC通道
void adc_channel_init(adc_channel_t* channel, 
                     uint16_t* raw_buffer, 
                     float32_t* float_buffer,
                     Point* point_buffer,
                     volatile int* done_flag,
                     uint16_t color,
                     const char* name,
                     const char* unit,
                     float conversion_factor) {
    if (channel == NULL) return;
    
    channel->raw_samples = raw_buffer;
    channel->float_samples = float_buffer;
    channel->prev_points = point_buffer;
    channel->done_flag = done_flag;
    channel->has_prev_points = false;
    channel->color = color;
    channel->name = name;
    channel->unit = unit;
    channel->conversion_factor = conversion_factor;
    channel->rms_value = 0.0f;
    channel->mean_value = 0.0f;
    channel->last_displayed_value = -1.0f;  // 初始化为无效值，强制首次更新
    channel->value_changed = true;
    channel->draw_count = 0;  // 初始化绘制计数器
}

// 处理ADC数据（计算RMS等）- 性能优化版本
void adc_channel_process_data(adc_channel_t* channel, uint16_t sample_count) {
    if (channel == NULL || channel->raw_samples == NULL || channel->float_samples == NULL) {
        return;
    }

    // 性能优化：使用CMSIS-DSP的批量转换函数
    // 1. 将 uint16_t 采样数据转换为 float32_t（优化版本）
    for (int i = 0; i < sample_count; i += 4) {
        // 批量处理4个样本，减少循环开销
        int remaining = (sample_count - i < 4) ? (sample_count - i) : 4;
        for (int j = 0; j < remaining; j++) {
            channel->float_samples[i + j] = (float32_t)channel->raw_samples[i + j];
        }
    }

    // 2. 计算直流偏置 (Mean)
    arm_mean_f32(channel->float_samples, sample_count, &channel->mean_value);

    // 3. 移除直流偏置 (Offset)
    arm_offset_f32(channel->float_samples, -channel->mean_value, channel->float_samples, sample_count);

    // 4. 计算处理后信号的 RMS
    float32_t rms_adc;
    arm_rms_f32(channel->float_samples, sample_count, &rms_adc);

    // 5. 转换为物理量
    float32_t new_rms = rms_adc * channel->conversion_factor;

    // 性能优化：检查值是否发生显著变化（避免频繁刷新显示）
    const float CHANGE_THRESHOLD = 0.001f;  // 变化阈值
    if (fabsf(new_rms - channel->last_displayed_value) > CHANGE_THRESHOLD) {
        channel->value_changed = true;
        channel->last_displayed_value = new_rms;
    } else {
        channel->value_changed = false;
    }

    channel->rms_value = new_rms;
}

// 绘制波形 - 优化版本（避免栈分配大数组）
void adc_channel_draw_waveform(adc_channel_t* channel,
                              uint16_t x0, uint16_t y0,
                              uint16_t width, uint16_t height,
                              uint16_t data_points,
                              uint16_t bgcolor) {
    if (channel == NULL || channel->raw_samples == NULL || channel->prev_points == NULL) {
        return;
    }

    // 安全检查：限制数据点数量，避免缓冲区溢出
    if (data_points > 32) {
        data_points = 32;
    }

    // 1. 如果有上一帧的波形，用背景色重新绘制它，实现擦除效果
    if (channel->has_prev_points) {
        for (int i = 0; i < data_points - 1; i++) {
            LCD_DrawLine(channel->prev_points[i].x, channel->prev_points[i].y,
                        channel->prev_points[i+1].x, channel->prev_points[i+1].y, bgcolor);
        }
    }

    // 2. 优化：直接计算并绘制，避免大数组分配
    float x_step = (float)(width - 1) / (data_points - 1);
    float y_ratio = (float)(height - 1) / 4095.0f;

    // 计算采样步长，从512个样本中均匀选择data_points个点（内存优化）
    uint16_t sample_step = 512 / data_points;

    Point prev_point, current_point;
    bool first_point = true;

    for (int i = 0; i < data_points; i++) {
        // 计算X坐标
        current_point.x = x0 + (uint16_t)(i * x_step);

        // 计算Y坐标，使用均匀采样（512点优化）
        uint16_t sample_index = i * sample_step;
        if (sample_index >= 512) sample_index = 511;  // 边界保护

        uint16_t raw_y = (uint16_t)(channel->raw_samples[sample_index] * y_ratio);
        if (raw_y >= height) raw_y = height - 1;
        current_point.y = y0 + (height - 1 - raw_y);

        // 绘制线段（除了第一个点）
        if (!first_point) {
            LCD_DrawLine(prev_point.x, prev_point.y,
                        current_point.x, current_point.y, channel->color);
        }

        // 保存当前点坐标，用于下一帧的擦除
        channel->prev_points[i] = current_point;

        prev_point = current_point;
        first_point = false;
    }

    channel->has_prev_points = true;
    channel->draw_count++;  // 更新绘制计数器
}

// 显示数值（性能优化版本）
void adc_channel_display_values(adc_channel_t* channel,
                               uint16_t x, uint16_t y,
                               uint16_t text_color, uint16_t bg_color) {
    if (channel == NULL) return;

    // 性能优化：只在值发生变化时才更新显示
    if (channel->value_changed) {
        LCD_ShowFloatNum1(x, y, channel->rms_value, 4, text_color, bg_color, 16);
        LCD_ShowString(x + 50, y, (const u8*)channel->unit, text_color, bg_color, 16, 0);
    }
}
