/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4150"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12  = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121 = ADC12.addInstance();
const Board  = scripting.addModule("/ti/driverlib/Board");
const DMA    = scripting.addModule("/ti/driverlib/DMA");
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
ADC121.$name                      = "ADC12_0";
ADC121.sampClkDiv                 = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.adcMem0chansel             = "DL_ADC12_INPUT_CHAN_2";
ADC121.sampleTime0                = "125 us";
ADC121.enabledInterrupts          = ["DL_ADC12_INTERRUPT_MEM0_RESULT_LOADED"];
ADC121.powerDownMode              = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.subChanID                  = 1;
ADC121.trigSrc                    = "DL_ADC12_TRIG_SRC_EVENT";
ADC121.configureDMA               = true;
ADC121.repeatMode                 = true;
ADC121.peripheral.adcPin2.$assign = "PA25";
ADC121.adcPin2Config.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
ADC121.DMA_CHANNEL.$name          = "DMA_CH0";


SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

TIMER1.$name                       = "TIMER_0";
TIMER1.timerMode                   = "PERIODIC";
TIMER1.counterZero                 = true;
TIMER1.timerPeriod                 = "100us";
TIMER1.event1ControllerInterruptEn = ["ZERO_EVENT"];
TIMER1.event2ControllerInterruptEn = ["ZERO_EVENT"];
TIMER1.event1PublisherChannel      = 1;
TIMER1.event2PublisherChannel      = 2;

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
ADC121.peripheral.$suggestSolution             = "ADC0";
ADC121.DMA_CHANNEL.peripheral.$suggestSolution = "DMA_CH0";
Board.peripheral.$suggestSolution              = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution     = "PA20";
Board.peripheral.swdioPin.$suggestSolution     = "PA19";
TIMER1.peripheral.$suggestSolution             = "TIMA0";
