
//*********************************************************************************//
//
//                                     _oo0oo_
//                                    o8888888o
//                                    88" . "88
//                                    (| -_- |)
//                                    0\  =  /0
//                                   __/·_ _·\__
//                                .' \\|     |// '.
//                               / \\|||  :  |||// \
//                              / _||||| -:- |||||_ \
//                             |   | \\\  -  /// |   |
//                             | \_|  ''\- -/''  |_/ |
//                             \  .-\__  '-'   __/-. /
//                           ___'. .'  /--.--\  '. .'___
//                        ."" '<    .__\_<|>_/__.    >' "".
//                       | | :   - \  .;\ _ / ;.  / -  : | |
//                       \  \ ·_.   \_ __\ / __ _/   .-· / /
//                    ====·-.___·-.__  \_____/ __.-·__.-· ====
//                                     '=---='
//
//             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
//
//                          佛祖保佑           永无bug
//
//
//********************************************************************************//
#include "ti_msp_dl_config.h"
#include <string.h> 

#define SAMPLES_PER_CHANNEL (1024)    // 注意：修改此值后，必须同时在SysConfig的DMA配置中修改Transfer Size

//数据存储数组
uint16_t gADC0_Data[SAMPLES_PER_CHANNEL] __attribute__((aligned(4)));
uint16_t gADC1_Data[SAMPLES_PER_CHANNEL] __attribute__((aligned(4)));

//完成标志
static volatile bool gADC0_DMADone = false;
static volatile bool gADC1_DMADone = false;


void reconfigure_and_restart_dma(void);

int main(void) {
    SYSCFG_DL_init();

   //优先使能ADC
    NVIC_EnableIRQ(ADC12_0_INST_INT_IRQN);
    NVIC_EnableIRQ(ADC12_1_INST_INT_IRQN);


    //启动DMA
    DL_DMA_setSrcAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)&ADC12_0_INST->ULLMEM.MEMRES[0]);
    DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)&gADC0_Data[0]);
    DL_DMA_setTransferSize(DMA, DMA_CH0_CHAN_ID, SAMPLES_PER_CHANNEL);
    DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);

    DL_DMA_setSrcAddr(DMA, DMA_CH1_CHAN_ID, (uint32_t)&ADC12_1_INST->ULLMEM.MEMRES[0]);
    DL_DMA_setDestAddr(DMA, DMA_CH1_CHAN_ID, (uint32_t)&gADC1_Data[0]);
    DL_DMA_setTransferSize(DMA, DMA_CH1_CHAN_ID, SAMPLES_PER_CHANNEL);
    DL_DMA_enableChannel(DMA, DMA_CH1_CHAN_ID);

    //启动定时器
    DL_TimerG_startCounter(TIMER_0_INST);

    while (1) {
     
        if (gADC0_DMADone && gADC1_DMADone) {
            
          
            DL_TimerG_stopCounter(TIMER_0_INST);//停止定时器


            __BKPT(0);

            reconfigure_and_restart_dma();
        }
        __WFI(); 
    }
}


void reconfigure_and_restart_dma(void)
{
    
    gADC0_DMADone = false;
    gADC1_DMADone = false;

   
    memset(gADC0_Data, 0, sizeof(gADC0_Data));
    memset(gADC1_Data, 0, sizeof(gADC1_Data));
    
  
    DL_DMA_setSrcAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)&ADC12_0_INST->ULLMEM.MEMRES[0]);
    DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t)&gADC0_Data[0]);
    DL_DMA_setTransferSize(DMA, DMA_CH0_CHAN_ID, SAMPLES_PER_CHANNEL);
    DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);

    DL_DMA_setSrcAddr(DMA, DMA_CH1_CHAN_ID, (uint32_t)&ADC12_1_INST->ULLMEM.MEMRES[0]);
    DL_DMA_setDestAddr(DMA, DMA_CH1_CHAN_ID, (uint32_t)&gADC1_Data[0]);
    DL_DMA_setTransferSize(DMA, DMA_CH1_CHAN_ID, SAMPLES_PER_CHANNEL);
    DL_DMA_enableChannel(DMA, DMA_CH1_CHAN_ID);

 
    DL_TimerG_startCounter(TIMER_0_INST);
}



void ADC12_0_INST_IRQHandler(void) 
{
   
    if (DL_ADC12_getPendingInterrupt(ADC12_0_INST) == DL_ADC12_IIDX_DMA_DONE) {
        gADC0_DMADone = true;
    }
}


void ADC12_1_INST_IRQHandler(void) 
{
   
    if (DL_ADC12_getPendingInterrupt(ADC12_1_INST) == DL_ADC12_IIDX_DMA_DONE) {
        gADC1_DMADone = true;
    }
}