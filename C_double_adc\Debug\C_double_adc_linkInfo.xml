<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\bin\tiarmlnk -IG:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib -o C_double_adc.out -mC_double_adc.map -iC:/TI/mspm0_sdk_2_05_01_00/source -iC:/Users/<USER>/workspace_ccstheia/C_double_adc -iC:/Users/<USER>/workspace_ccstheia/C_double_adc/Debug/syscfg -iG:/ti/ccs2011/ccs/tools/compiler/ti-cgt-armllvm_4.0.2.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=C_double_adc_linkInfo.xml --rom_model ./adc_to_uart.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=0</command_line>
   <link_time>0x68764745</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\C_double_adc\Debug\C_double_adc.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x79d</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\C_double_adc\Debug\.\</path>
         <kind>object</kind>
         <file>adc_to_uart.o</file>
         <name>adc_to_uart.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\C_double_adc\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\C_double_adc\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-10">
         <path>C:\Users\<USER>\workspace_ccstheia\C_double_adc\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_adc12.o</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_dma.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\TI\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-2b">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-2d">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-2e">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d5">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>G:\ti\ccs2011\ccs\tools\compiler\ti-cgt-armllvm_4.0.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.text.main</name>
         <load_address>0x1a8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a8</run_address>
         <size>0xc4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.text.SYSCFG_DL_ADC12_0_init</name>
         <load_address>0x26c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x26c</run_address>
         <size>0x88</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.text.SYSCFG_DL_ADC12_1_init</name>
         <load_address>0x2f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2f4</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x370</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x370</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-af">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x3ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3ec</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.text.SYSCFG_DL_TIMER_0_init</name>
         <load_address>0x454</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x454</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.text.DL_ADC12_initSingleSample</name>
         <load_address>0x4ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4ac</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.DL_DMA_initChannel</name>
         <load_address>0x4f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4f8</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.text.DL_ADC12_configConversionMem</name>
         <load_address>0x544</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x544</run_address>
         <size>0x4a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text.DL_ADC12_setClockConfig</name>
         <load_address>0x590</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x590</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-96">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x5d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5d0</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.text.DL_Timer_setPublisherChanID</name>
         <load_address>0x60c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x60c</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.text.DL_ADC12_getMemResult</name>
         <load_address>0x644</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x644</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.text.DL_ADC12_setDMASamplesCnt</name>
         <load_address>0x674</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x674</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-83">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x6a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a0</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-88">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x6cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6cc</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x6f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f8</run_address>
         <size>0x2a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x722</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x722</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.text.DL_Timer_enableEvent</name>
         <load_address>0x74c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x74c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x774</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x774</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x79c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x79c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.ADC1_IRQHandler</name>
         <load_address>0x7c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7c4</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.ADC0_IRQHandler</name>
         <load_address>0x7e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7e8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.text.DL_ADC12_setPowerDownMode</name>
         <load_address>0x808</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x808</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.text.DL_ADC12_clearInterruptStatus</name>
         <load_address>0x828</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x828</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.text.DL_ADC12_enableDMA</name>
         <load_address>0x844</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x844</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.text.DL_ADC12_enableInterrupt</name>
         <load_address>0x860</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x860</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text.DL_SYSCTL_setMCLKDivider</name>
         <load_address>0x87c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x87c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x898</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x898</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0x8b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8b4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x8d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8d0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.text.DL_ADC12_setSubscriberChanID</name>
         <load_address>0x8ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8ec</run_address>
         <size>0x1a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.text.DL_ADC12_enablePower</name>
         <load_address>0x908</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x908</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.text.DL_ADC12_reset</name>
         <load_address>0x920</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x920</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.text.DL_ADC12_setSampleTime0</name>
         <load_address>0x938</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x938</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x950</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x950</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x968</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x968</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.DL_GPIO_initDigitalOutput</name>
         <load_address>0x980</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x980</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x998</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x998</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x9b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9b0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x9c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9c8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x9e0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9e0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-89">
         <name>.text.DL_Timer_startCounter</name>
         <load_address>0x9f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9f8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text.DL_Timer_stopCounter</name>
         <load_address>0xa10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa10</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.SYSCFG_DL_DMA_CH0_init</name>
         <load_address>0xa28</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa28</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text.DL_ADC12_enableConversions</name>
         <load_address>0xa40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa40</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0xa56</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa56</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0xa6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa6c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0xa80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa80</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.text.DL_ADC12_getPendingInterrupt</name>
         <load_address>0xa94</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa94</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-79">
         <name>.text:TI_memcpy_small</name>
         <load_address>0xaa6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaa6</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0xab8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xab8</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0xacc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xacc</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-52">
         <name>.text:decompress:ZI</name>
         <load_address>0xadc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xadc</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.text:TI_memset_small</name>
         <load_address>0xaec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaec</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-65">
         <name>.text.__aeabi_memclr</name>
         <load_address>0xafc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xafc</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0xb08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb08</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.text.SYSCFG_DL_DMA_init</name>
         <load_address>0xb12</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb12</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0xb1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb1c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.text:abort</name>
         <load_address>0xb24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb24</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0xb2a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb2a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.text.HOSTexit</name>
         <load_address>0xb2e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb2e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0xb32</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb32</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.text._system_pre_init</name>
         <load_address>0xb36</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb36</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-156">
         <name>__TI_handler_table</name>
         <load_address>0xb80</load_address>
         <readonly>true</readonly>
         <run_address>0xb80</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-159">
         <name>.cinit..bss.load</name>
         <load_address>0xb8c</load_address>
         <readonly>true</readonly>
         <run_address>0xb8c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-158">
         <name>.cinit..data.load</name>
         <load_address>0xb94</load_address>
         <readonly>true</readonly>
         <run_address>0xb94</run_address>
         <size>0x7</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-157">
         <name>__TI_cinit_table</name>
         <load_address>0xb9c</load_address>
         <readonly>true</readonly>
         <run_address>0xb9c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-113">
         <name>.rodata.gDMA_CH0Config</name>
         <load_address>0xb40</load_address>
         <readonly>true</readonly>
         <run_address>0xb40</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.rodata.gTIMER_0TimerConfig</name>
         <load_address>0xb58</load_address>
         <readonly>true</readonly>
         <run_address>0xb58</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.rodata.gADC12_0ClockConfig</name>
         <load_address>0xb6c</load_address>
         <readonly>true</readonly>
         <run_address>0xb6c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.rodata.gADC12_1ClockConfig</name>
         <load_address>0xb74</load_address>
         <readonly>true</readonly>
         <run_address>0xb74</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.rodata.gTIMER_0ClockConfig</name>
         <load_address>0xb7c</load_address>
         <readonly>true</readonly>
         <run_address>0xb7c</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-120">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-8c">
         <name>.data.gSampleIndex</name>
         <load_address>0x202010c0</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202010c0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.common:gCheckADC0_Done</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010bc</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-60">
         <name>.common:gCheckADC1_Done</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010bd</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-8d">
         <name>.common:gAdcWaveform0</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x800</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-8e">
         <name>.common:gAdcWaveform1</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200800</run_address>
         <size>0x800</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-b6">
         <name>.common:gTIMER_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20201000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-15b">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1a1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_abbrev</name>
         <load_address>0x1a1</load_address>
         <run_address>0x1a1</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_abbrev</name>
         <load_address>0x362</load_address>
         <run_address>0x362</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.debug_abbrev</name>
         <load_address>0x3cf</load_address>
         <run_address>0x3cf</run_address>
         <size>0x171</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-107">
         <name>.debug_abbrev</name>
         <load_address>0x540</load_address>
         <run_address>0x540</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-114">
         <name>.debug_abbrev</name>
         <load_address>0x5a2</load_address>
         <run_address>0x5a2</run_address>
         <size>0x180</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_abbrev</name>
         <load_address>0x722</load_address>
         <run_address>0x722</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_abbrev</name>
         <load_address>0x9a8</load_address>
         <run_address>0x9a8</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-c3">
         <name>.debug_abbrev</name>
         <load_address>0xa57</load_address>
         <run_address>0xa57</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_abbrev</name>
         <load_address>0xbc7</load_address>
         <run_address>0xbc7</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_abbrev</name>
         <load_address>0xc00</load_address>
         <run_address>0xc00</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_abbrev</name>
         <load_address>0xcc2</load_address>
         <run_address>0xcc2</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_abbrev</name>
         <load_address>0xd32</load_address>
         <run_address>0xd32</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_abbrev</name>
         <load_address>0xdbf</load_address>
         <run_address>0xdbf</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_abbrev</name>
         <load_address>0xe57</load_address>
         <run_address>0xe57</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_abbrev</name>
         <load_address>0xe83</load_address>
         <run_address>0xe83</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_abbrev</name>
         <load_address>0xeaa</load_address>
         <run_address>0xeaa</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_abbrev</name>
         <load_address>0xecf</load_address>
         <run_address>0xecf</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_abbrev</name>
         <load_address>0xef4</load_address>
         <run_address>0xef4</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.debug_abbrev</name>
         <load_address>0xf19</load_address>
         <run_address>0xf19</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1015</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_info</name>
         <load_address>0x1015</load_address>
         <run_address>0x1015</run_address>
         <size>0x2c96</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x3cab</load_address>
         <run_address>0x3cab</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_info</name>
         <load_address>0x3d2b</load_address>
         <run_address>0x3d2b</run_address>
         <size>0x745</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_info</name>
         <load_address>0x4470</load_address>
         <run_address>0x4470</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_info</name>
         <load_address>0x44e5</load_address>
         <run_address>0x44e5</run_address>
         <size>0x6ea</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_info</name>
         <load_address>0x4bcf</load_address>
         <run_address>0x4bcf</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x7d41</load_address>
         <run_address>0x7d41</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_info</name>
         <load_address>0x8164</load_address>
         <run_address>0x8164</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_info</name>
         <load_address>0x88a8</load_address>
         <run_address>0x88a8</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_info</name>
         <load_address>0x88ee</load_address>
         <run_address>0x88ee</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0x8a80</load_address>
         <run_address>0x8a80</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0x8b46</load_address>
         <run_address>0x8b46</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_info</name>
         <load_address>0x8cc2</load_address>
         <run_address>0x8cc2</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_info</name>
         <load_address>0x8dba</load_address>
         <run_address>0x8dba</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_info</name>
         <load_address>0x8df5</load_address>
         <run_address>0x8df5</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-67">
         <name>.debug_info</name>
         <load_address>0x8f8e</load_address>
         <run_address>0x8f8e</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_info</name>
         <load_address>0x9143</load_address>
         <run_address>0x9143</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_info</name>
         <load_address>0x943d</load_address>
         <run_address>0x943d</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.debug_info</name>
         <load_address>0x9681</load_address>
         <run_address>0x9681</run_address>
         <size>0xac</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_ranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_ranges</name>
         <load_address>0x190</load_address>
         <run_address>0x190</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_ranges</name>
         <load_address>0x1a8</load_address>
         <run_address>0x1a8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_ranges</name>
         <load_address>0x1c0</load_address>
         <run_address>0x1c0</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_ranges</name>
         <load_address>0x398</load_address>
         <run_address>0x398</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_ranges</name>
         <load_address>0x3e0</load_address>
         <run_address>0x3e0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_ranges</name>
         <load_address>0x428</load_address>
         <run_address>0x428</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x440</load_address>
         <run_address>0x440</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_ranges</name>
         <load_address>0x490</load_address>
         <run_address>0x490</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_ranges</name>
         <load_address>0x4a8</load_address>
         <run_address>0x4a8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_ranges</name>
         <load_address>0x4d0</load_address>
         <run_address>0x4d0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_ranges</name>
         <load_address>0x4f8</load_address>
         <run_address>0x4f8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xba5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_str</name>
         <load_address>0xba5</load_address>
         <run_address>0xba5</run_address>
         <size>0x2359</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_str</name>
         <load_address>0x2efe</load_address>
         <run_address>0x2efe</run_address>
         <size>0x163</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.debug_str</name>
         <load_address>0x3061</load_address>
         <run_address>0x3061</run_address>
         <size>0x631</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-108">
         <name>.debug_str</name>
         <load_address>0x3692</load_address>
         <run_address>0x3692</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-115">
         <name>.debug_str</name>
         <load_address>0x37ff</load_address>
         <run_address>0x37ff</run_address>
         <size>0x64a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_str</name>
         <load_address>0x3e49</load_address>
         <run_address>0x3e49</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_str</name>
         <load_address>0x5c15</load_address>
         <run_address>0x5c15</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-c4">
         <name>.debug_str</name>
         <load_address>0x5e3a</load_address>
         <run_address>0x5e3a</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_str</name>
         <load_address>0x6169</load_address>
         <run_address>0x6169</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_str</name>
         <load_address>0x625e</load_address>
         <run_address>0x625e</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_str</name>
         <load_address>0x63f9</load_address>
         <run_address>0x63f9</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_str</name>
         <load_address>0x6561</load_address>
         <run_address>0x6561</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_str</name>
         <load_address>0x6736</load_address>
         <run_address>0x6736</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_str</name>
         <load_address>0x687e</load_address>
         <run_address>0x687e</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_frame</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x3ac</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x46c</load_address>
         <run_address>0x46c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_frame</name>
         <load_address>0x49c</load_address>
         <run_address>0x49c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_frame</name>
         <load_address>0x4e8</load_address>
         <run_address>0x4e8</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_frame</name>
         <load_address>0x508</load_address>
         <run_address>0x508</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_frame</name>
         <load_address>0x538</load_address>
         <run_address>0x538</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_frame</name>
         <load_address>0x940</load_address>
         <run_address>0x940</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_frame</name>
         <load_address>0x9d0</load_address>
         <run_address>0x9d0</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_frame</name>
         <load_address>0xad0</load_address>
         <run_address>0xad0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_frame</name>
         <load_address>0xaf0</load_address>
         <run_address>0xaf0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_frame</name>
         <load_address>0xb28</load_address>
         <run_address>0xb28</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0xb50</load_address>
         <run_address>0xb50</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_frame</name>
         <load_address>0xb80</load_address>
         <run_address>0xb80</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_frame</name>
         <load_address>0xbb0</load_address>
         <run_address>0xbb0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x3f9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_line</name>
         <load_address>0x3f9</load_address>
         <run_address>0x3f9</run_address>
         <size>0x972</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0xd6b</load_address>
         <run_address>0xd6b</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_line</name>
         <load_address>0xe23</load_address>
         <run_address>0xe23</run_address>
         <size>0x27f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.debug_line</name>
         <load_address>0x10a2</load_address>
         <run_address>0x10a2</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_line</name>
         <load_address>0x121a</load_address>
         <run_address>0x121a</run_address>
         <size>0x248</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_line</name>
         <load_address>0x1462</load_address>
         <run_address>0x1462</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_line</name>
         <load_address>0x2bd0</load_address>
         <run_address>0x2bd0</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_line</name>
         <load_address>0x2dac</load_address>
         <run_address>0x2dac</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_line</name>
         <load_address>0x32c6</load_address>
         <run_address>0x32c6</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_line</name>
         <load_address>0x3304</load_address>
         <run_address>0x3304</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_line</name>
         <load_address>0x3402</load_address>
         <run_address>0x3402</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x34c2</load_address>
         <run_address>0x34c2</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-92">
         <name>.debug_line</name>
         <load_address>0x368a</load_address>
         <run_address>0x368a</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_line</name>
         <load_address>0x36f1</load_address>
         <run_address>0x36f1</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_line</name>
         <load_address>0x3732</load_address>
         <run_address>0x3732</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_line</name>
         <load_address>0x37d6</load_address>
         <run_address>0x37d6</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_line</name>
         <load_address>0x3890</load_address>
         <run_address>0x3890</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_line</name>
         <load_address>0x3930</load_address>
         <run_address>0x3930</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-11"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_loc</name>
         <load_address>0xc7</load_address>
         <run_address>0xc7</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-116">
         <name>.debug_loc</name>
         <load_address>0xda</load_address>
         <run_address>0xda</run_address>
         <size>0xd0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_loc</name>
         <load_address>0x1aa</load_address>
         <run_address>0x1aa</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-14"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_loc</name>
         <load_address>0x1bd1</load_address>
         <run_address>0x1bd1</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2b"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_loc</name>
         <load_address>0x1ca9</load_address>
         <run_address>0x1ca9</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2d"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_loc</name>
         <load_address>0x20cd</load_address>
         <run_address>0x20cd</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x2239</load_address>
         <run_address>0x2239</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_loc</name>
         <load_address>0x22a8</load_address>
         <run_address>0x22a8</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_loc</name>
         <load_address>0x240f</load_address>
         <run_address>0x240f</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-66">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d7"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_aranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_aranges</name>
         <load_address>0x70</load_address>
         <run_address>0x70</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0xa80</size>
         <contents>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-6a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0xb80</load_address>
         <run_address>0xb80</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-156"/>
            <object_component_ref idref="oc-159"/>
            <object_component_ref idref="oc-158"/>
            <object_component_ref idref="oc-157"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0xb40</load_address>
         <run_address>0xb40</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-e7"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-120"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202010c0</run_address>
         <size>0x4</size>
         <contents>
            <object_component_ref idref="oc-8c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x10be</size>
         <contents>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-b6"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-15b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-117" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-118" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-119" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11a" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11b" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11c" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11e" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13a" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf28</size>
         <contents>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-c3"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-15d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-13c" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x972d</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-15c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-13e" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x520</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-aa"/>
         </contents>
      </logical_group>
      <logical_group id="lg-140" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x6967</size>
         <contents>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-c4"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-fd"/>
         </contents>
      </logical_group>
      <logical_group id="lg-142" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xbd0</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-bc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-144" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x39b0</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-ad"/>
         </contents>
      </logical_group>
      <logical_group id="lg-146" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2435</size>
         <contents>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-95"/>
         </contents>
      </logical_group>
      <logical_group id="lg-150" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x98</size>
         <contents>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-ac"/>
         </contents>
      </logical_group>
      <logical_group id="lg-15a" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-16a" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xbb0</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-16b" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x10c4</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-16c" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0xbb0</used_space>
         <unused_space>0x1f450</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0xa80</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xb40</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xb80</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0xbb0</start_address>
               <size>0x1f450</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x12c2</used_space>
         <unused_space>0x6d3e</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-11c"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-11e"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x10be</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x202010be</start_address>
               <size>0x2</size>
            </available_space>
            <allocated_space>
               <start_address>0x202010c0</start_address>
               <size>0x4</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202010c4</start_address>
               <size>0x6d3c</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0xb8c</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x10be</run_size>
            <compression>zero_init</compression>
         </cprec>
         <cprec>
            <name>.data</name>
            <load_address>0xb94</load_address>
            <load_size>0x7</load_size>
            <run_address>0x202010c0</run_address>
            <run_size>0x4</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0xb9c</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0xbac</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0xbac</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0xb80</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0xb8c</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-55">
         <name>main</name>
         <value>0x1a9</value>
         <object_component_ref idref="oc-6e"/>
      </symbol>
      <symbol id="sm-56">
         <name>gSampleIndex</name>
         <value>0x202010c0</value>
         <object_component_ref idref="oc-8c"/>
      </symbol>
      <symbol id="sm-57">
         <name>gCheckADC0_Done</name>
         <value>0x202010bc</value>
      </symbol>
      <symbol id="sm-58">
         <name>gCheckADC1_Done</name>
         <value>0x202010bd</value>
      </symbol>
      <symbol id="sm-59">
         <name>gAdcWaveform0</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5a">
         <name>gAdcWaveform1</name>
         <value>0x20200800</value>
      </symbol>
      <symbol id="sm-5b">
         <name>ADC0_IRQHandler</name>
         <value>0x7e9</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-5c">
         <name>ADC1_IRQHandler</name>
         <value>0x7c5</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-f4">
         <name>SYSCFG_DL_init</name>
         <value>0x6a1</value>
         <object_component_ref idref="oc-83"/>
      </symbol>
      <symbol id="sm-f5">
         <name>SYSCFG_DL_initPower</name>
         <value>0x3ed</value>
         <object_component_ref idref="oc-af"/>
      </symbol>
      <symbol id="sm-f6">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x775</value>
         <object_component_ref idref="oc-b0"/>
      </symbol>
      <symbol id="sm-f7">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x6f9</value>
         <object_component_ref idref="oc-b1"/>
      </symbol>
      <symbol id="sm-f8">
         <name>SYSCFG_DL_TIMER_0_init</name>
         <value>0x455</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-f9">
         <name>SYSCFG_DL_ADC12_0_init</name>
         <value>0x26d</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-fa">
         <name>SYSCFG_DL_ADC12_1_init</name>
         <value>0x2f5</value>
         <object_component_ref idref="oc-b4"/>
      </symbol>
      <symbol id="sm-fb">
         <name>SYSCFG_DL_DMA_init</name>
         <value>0xb13</value>
         <object_component_ref idref="oc-b5"/>
      </symbol>
      <symbol id="sm-fc">
         <name>gTIMER_0Backup</name>
         <value>0x20201000</value>
      </symbol>
      <symbol id="sm-fd">
         <name>SYSCFG_DL_DMA_CH0_init</name>
         <value>0xa29</value>
         <object_component_ref idref="oc-fb"/>
      </symbol>
      <symbol id="sm-108">
         <name>Default_Handler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-109">
         <name>Reset_Handler</name>
         <value>0xb33</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-10a">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-10b">
         <name>NMI_Handler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-10c">
         <name>HardFault_Handler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-10d">
         <name>SVC_Handler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-10e">
         <name>PendSV_Handler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-10f">
         <name>SysTick_Handler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-110">
         <name>GROUP0_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-111">
         <name>GROUP1_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-112">
         <name>TIMG8_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-113">
         <name>UART3_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-114">
         <name>CANFD0_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-115">
         <name>DAC0_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-116">
         <name>SPI0_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-117">
         <name>SPI1_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-118">
         <name>UART1_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-119">
         <name>UART2_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-11a">
         <name>UART0_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-11b">
         <name>TIMG0_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-11c">
         <name>TIMG6_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-11d">
         <name>TIMA0_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-11e">
         <name>TIMA1_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-11f">
         <name>TIMG7_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-120">
         <name>TIMG12_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-121">
         <name>I2C0_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-122">
         <name>I2C1_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-123">
         <name>AES_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-124">
         <name>RTC_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-125">
         <name>DMA_IRQHandler</name>
         <value>0xb2b</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-126">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-127">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-128">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-129">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-12a">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-12b">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-12c">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-12d">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-12e">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-139">
         <name>DL_ADC12_setClockConfig</name>
         <value>0x591</value>
         <object_component_ref idref="oc-e9"/>
      </symbol>
      <symbol id="sm-142">
         <name>DL_Common_delayCycles</name>
         <value>0xb09</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-14c">
         <name>DL_DMA_initChannel</name>
         <value>0x4f9</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-15c">
         <name>DL_Timer_setClockConfig</name>
         <value>0x8d1</value>
         <object_component_ref idref="oc-dd"/>
      </symbol>
      <symbol id="sm-15d">
         <name>DL_Timer_initTimerMode</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-e3"/>
      </symbol>
      <symbol id="sm-168">
         <name>_c_int00_noargs</name>
         <value>0x79d</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-169">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-175">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x5d1</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-17d">
         <name>_system_pre_init</name>
         <value>0xb37</value>
         <object_component_ref idref="oc-6a"/>
      </symbol>
      <symbol id="sm-188">
         <name>__TI_zero_init</name>
         <value>0xadd</value>
         <object_component_ref idref="oc-52"/>
      </symbol>
      <symbol id="sm-191">
         <name>__TI_decompress_none</name>
         <value>0xab9</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-19c">
         <name>__TI_decompress_lzss</name>
         <value>0x371</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-1a6">
         <name>abort</name>
         <value>0xb25</value>
         <object_component_ref idref="oc-8f"/>
      </symbol>
      <symbol id="sm-1b0">
         <name>HOSTexit</name>
         <value>0xb2f</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-1b1">
         <name>C$$EXIT</name>
         <value>0xb2e</value>
         <object_component_ref idref="oc-b9"/>
      </symbol>
      <symbol id="sm-1b7">
         <name>__aeabi_memcpy</name>
         <value>0xb1d</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-1b8">
         <name>__aeabi_memcpy4</name>
         <value>0xb1d</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-1b9">
         <name>__aeabi_memcpy8</name>
         <value>0xb1d</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-1c0">
         <name>__aeabi_memclr</name>
         <value>0xafd</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-1c1">
         <name>__aeabi_memclr4</name>
         <value>0xafd</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-1c2">
         <name>__aeabi_memclr8</name>
         <value>0xafd</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-1cb">
         <name>TI_memcpy_small</name>
         <value>0xaa7</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-1d4">
         <name>TI_memset_small</name>
         <value>0xaed</value>
         <object_component_ref idref="oc-a9"/>
      </symbol>
      <symbol id="sm-1d5">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d8">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1d9">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>
