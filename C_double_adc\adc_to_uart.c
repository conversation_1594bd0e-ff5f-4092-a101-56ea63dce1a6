
#include "ti_msp_dl_config.h"


#define SAMPLES_TO_COLLECT (1024)

volatile bool gCheckADC0_Done;
volatile bool gCheckADC1_Done;

volatile uint16_t gAdcWaveform0[SAMPLES_TO_COLLECT];
volatile uint16_t gAdcWaveform1[SAMPLES_TO_COLLECT];

volatile uint32_t gSampleIndex = 0;


int main(void)
{
    SYSCFG_DL_init();

    NVIC_EnableIRQ(ADC12_0_INST_INT_IRQN);
    NVIC_EnableIRQ(ADC12_1_INST_INT_IRQN);

  
    DL_Timer_startCounter(TIMER_0_INST);


    while (gSampleIndex < SAMPLES_TO_COLLECT) {

        gCheckADC0_Done = false;
        gCheckADC1_Done = false;

      
        while ((gCheckADC0_Done == false) || (gCheckADC1_Done == false)) {
            __WFE();
        }

 
        uint16_t adcResult0 = DL_ADC12_getMemResult(ADC12_0_INST, DL_ADC12_MEM_IDX_0);
        uint16_t adcResult1 = DL_ADC12_getMemResult(ADC12_1_INST, DL_ADC12_MEM_IDX_0);

        gAdcWaveform0[gSampleIndex] = adcResult0;
        gAdcWaveform1[gSampleIndex] = adcResult1;


        gSampleIndex++;
    }


    DL_Timer_stopCounter(TIMER_0_INST);

 
    while (1) {
        __WFE();
    }
}

void ADC12_0_INST_IRQHandler(void)
{
    switch (DL_ADC12_getPendingInterrupt(ADC12_0_INST)) {
        case DL_ADC12_IIDX_MEM0_RESULT_LOADED:
            gCheckADC0_Done = true;
            break;
        default:
            break;
    }
}

void ADC12_1_INST_IRQHandler(void)
{
    switch (DL_ADC12_getPendingInterrupt(ADC12_1_INST)) {
        case DL_ADC12_IIDX_MEM0_RESULT_LOADED:
            gCheckADC1_Done = true;

            break;
        default:
            break;
    }
}